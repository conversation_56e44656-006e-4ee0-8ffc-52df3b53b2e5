from pydantic import BaseModel, EmailStr, Field, validator, root_validator
from datetime import datetime
from typing import Optional, List, Union, Dict, Any
from enum import Enum

# Enums atualizados para maior abrangência
class TransactionType(str, Enum):
    INCOME = "income"
    EXPENSE = "expense"
    TRANSFER = "transfer"
    INVESTMENT = "investment"

# Subtipos de transação ampliados
class TransactionSubType(str, Enum):
    PURCHASE = "purchase"
    REFUND = "refund"
    PAYMENT = "payment"
    TRANSFER = "transfer"
    INVESTMENT_DEPOSIT = "investment_deposit"
    INVESTMENT_WITHDRAWAL = "investment_withdrawal"
    CREDIT_CARD_PAYMENT = "credit_card_payment"
    CREDIT_CARD_PURCHASE = "credit_card_purchase"
    OTHER = "other"

# Tipos de investimentos
class InvestmentType(str, Enum):
    SAVINGS = "savings"
    STOCKS = "stocks"
    BONDS = "bonds"
    REAL_ESTATE = "real_estate"
    CRYPTO = "crypto"
    FUNDS = "funds"
    OTHER = "other"

# Status de metas financeiras
class GoalStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELED = "canceled"

# Schemas para Usuário
class UserBase(BaseModel):
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    is_active: bool
    is_admin: Optional[bool] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    is_verified: Optional[bool] = None
    google_id: Optional[str] = None
    profile_picture: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Schemas para Transação
class TransactionBase(BaseModel):
    description: str
    amount: float
    type: str  # Mantido para compatibilidade
    category: str
    date: Optional[datetime] = None
    subtype: Optional[str] = None  # Mantido para compatibilidade

    # Novos campos
    transaction_type: Optional[str] = None  # TransactionType
    transaction_subtype: Optional[str] = None  # TransactionSubType
    credit_card_id: Optional[int] = None
    investment_id: Optional[int] = None
    related_transaction_id: Optional[int] = None

class TransactionCreate(TransactionBase):
    pass

class Transaction(TransactionBase):
    id: int
    user_id: int
    date: datetime

    class Config:
        from_attributes = True

class TransactionUpdate(BaseModel):
    description: Optional[str] = None
    amount: Optional[float] = None
    type: Optional[str] = None
    category: Optional[str] = None
    date: Optional[datetime] = None
    subtype: Optional[str] = None
    transaction_type: Optional[str] = None
    transaction_subtype: Optional[str] = None
    credit_card_id: Optional[int] = None
    investment_id: Optional[int] = None
    related_transaction_id: Optional[int] = None

# Schemas para Investimentos
class InvestmentBase(BaseModel):
    name: str
    description: Optional[str] = None
    type: str = Field(default="other")  # InvestmentType
    initial_amount: float
    current_amount: Optional[float] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    interest_rate: Optional[float] = None

class InvestmentCreate(InvestmentBase):
    pass

class Investment(InvestmentBase):
    id: int
    user_id: int
    current_amount: float
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        from_attributes = True

class InvestmentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    initial_amount: Optional[float] = None
    current_amount: Optional[float] = None
    end_date: Optional[datetime] = None
    interest_rate: Optional[float] = None

# Schemas para Cartão de Crédito
class CreditCardBase(BaseModel):
    name: str
    last_digits: Optional[str] = None
    closing_day: int  # Dia de fechamento da fatura (1-31)
    due_day: int  # Dia de vencimento da fatura (1-31)
    credit_limit: float
    current_balance: Optional[float] = 0

    @validator('closing_day')
    def validate_closing_day(cls, v):
        if v < 1 or v > 31:
            raise ValueError('O dia de fechamento deve estar entre 1 e 31')
        return v

    @validator('due_day')
    def validate_due_day(cls, v):
        if v < 1 or v > 31:
            raise ValueError('O dia de vencimento deve estar entre 1 e 31')
        return v

class CreditCardCreate(CreditCardBase):
    pass

class CreditCard(CreditCardBase):
    id: int
    user_id: int
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        from_attributes = True

class CreditCardUpdate(BaseModel):
    name: Optional[str] = None
    last_digits: Optional[str] = None
    closing_day: Optional[int] = None
    due_day: Optional[int] = None
    credit_limit: Optional[float] = None
    current_balance: Optional[float] = None

# Schemas para Fatura de Cartão de Crédito
class CreditCardStatementBase(BaseModel):
    credit_card_id: int
    statement_date: datetime
    due_date: datetime
    total_amount: float
    paid_amount: Optional[float] = 0
    is_paid: Optional[bool] = False
    payment_date: Optional[datetime] = None
    payment_transaction_id: Optional[int] = None

class CreditCardStatementCreate(CreditCardStatementBase):
    pass

class CreditCardStatement(CreditCardStatementBase):
    id: int

    class Config:
        from_attributes = True

class CreditCardStatementUpdate(BaseModel):
    paid_amount: Optional[float] = None
    is_paid: Optional[bool] = None
    payment_date: Optional[datetime] = None
    payment_transaction_id: Optional[int] = None

# Schemas para Metas Financeiras
class FinancialGoalBase(BaseModel):
    name: str
    description: Optional[str] = None
    target_amount: float
    current_amount: Optional[float] = 0
    start_date: Optional[datetime] = None
    target_date: Optional[datetime] = None
    status: Optional[str] = "active"  # GoalStatus

class FinancialGoalCreate(FinancialGoalBase):
    pass

class FinancialGoal(FinancialGoalBase):
    id: int
    user_id: int
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        from_attributes = True

class FinancialGoalUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    target_amount: Optional[float] = None
    current_amount: Optional[float] = None
    target_date: Optional[datetime] = None
    status: Optional[str] = None

# Schemas para Contribuições para Metas
class ContributionBase(BaseModel):
    amount: float
    date: Optional[datetime] = None
    description: Optional[str] = None

class ContributionCreate(ContributionBase):
    pass

class Contribution(ContributionBase):
    id: int
    goal_id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ContributionUpdate(BaseModel):
    amount: Optional[float] = None
    description: Optional[str] = None

# Schemas para Contribuições para Metas (compatibilidade)
class GoalContributionBase(BaseModel):
    amount: float
    date: Optional[datetime] = None
    transaction_id: Optional[int] = None
    goal_id: int

class GoalContributionCreate(GoalContributionBase):
    pass

class GoalContribution(GoalContributionBase):
    id: int

    class Config:
        from_attributes = True

class GoalContributionUpdate(BaseModel):
    amount: Optional[float] = None
    transaction_id: Optional[int] = None

# Schemas para Orçamentos
class BudgetBase(BaseModel):
    category: str
    monthly_limit: float
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class BudgetCreate(BudgetBase):
    pass

class Budget(BudgetBase):
    id: int
    user_id: int
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        from_attributes = True

class BudgetUpdate(BaseModel):
    category: Optional[str] = None
    monthly_limit: Optional[float] = None
    end_date: Optional[datetime] = None

# Schemas para respostas agregadas
class DateRangeParams(BaseModel):
    start_date: datetime
    end_date: datetime

class MonthlySummary(BaseModel):
    income: float
    expenses: float
    balance: float
    month: int
    year: int
    savings_rate: Optional[float] = None

class CategorySummary(BaseModel):
    category: str
    amount: float
    percentage: float

class FinancialSummary(BaseModel):
    total_income: float
    total_expenses: float
    balance: float
    expenses_by_category: List[CategorySummary]
    monthly_summary: List[MonthlySummary]