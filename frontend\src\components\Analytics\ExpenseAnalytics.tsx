import React from 'react';
import { Box, Typography, Grid } from '@mui/material';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

// Registrar os componentes necessários do Chart.js
ChartJS.register(ArcElement, Tooltip, Legend);

interface ExpenseAnalyticsProps {
  expenses: any[];
}

const ExpenseAnalytics: React.FC<ExpenseAnalyticsProps> = ({ expenses }) => {
  // Agrupar despesas por categoria
  const expensesByCategory = expenses.reduce((acc: { [key: string]: number }, expense) => {
    const category = expense.category || 'Outros';
    acc[category] = (acc[category] || 0) + expense.amount;
    return acc;
  }, {});

  // Preparar dados para o gráfico
  const chartData = {
    labels: Object.keys(expensesByCategory),
    datasets: [
      {
        data: Object.values(expensesByCategory),
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#8AC926',
          '#1982C4',
          '#6A4C93',
          '#FF595E',
        ],
        hoverBackgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#8AC926',
          '#1982C4',
          '#6A4C93',
          '#FF595E',
        ],
      },
    ],
  };

  // Calcular total de despesas
  const totalExpenses = expenses.reduce((sum: number, expense) => sum + expense.amount, 0);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Análise de Despesas
      </Typography>
      
      {expenses.length === 0 ? (
        <Typography>Nenhuma despesa registrada para o período selecionado.</Typography>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Pie data={chartData} />
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Total de Despesas: {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
              }).format(totalExpenses)}
            </Typography>
            
            <Typography variant="subtitle1" gutterBottom>
              Despesas por Categoria:
            </Typography>
            
            {Object.entries(expensesByCategory).map(([category, amount]) => (
              <Box key={category} sx={{ mb: 1 }}>
                <Typography variant="body1">
                  {category}: {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL',
                  }).format(amount as number)} ({((amount as number / totalExpenses) * 100).toFixed(1)}%)
                </Typography>
              </Box>
            ))}
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ExpenseAnalytics;