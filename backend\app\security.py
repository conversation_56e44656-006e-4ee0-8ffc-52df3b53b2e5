from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from sqlalchemy.orm import Session
from .schemas.financial import UserCreate, User
from .database import get_db
from .models.financial import User as UserModel
import os
import secrets
import logging
from pydantic_settings import BaseSettings
from pydantic import Field

# Configuração de logging dedicada
logger = logging.getLogger(__name__)

# Settings com variáveis de segurança
class SecuritySettings(BaseSettings):
    # Usar uma chave fixa para desenvolvimento para evitar problemas com reloads automáticos
    # Em produção, isso deve ser definido como uma variável de ambiente
    SECRET_KEY: str = Field(
        default="3ccb93593cb1a3aa4b7a842166cb47c230d813ed6372f1c1fe255e2443654cf5",
        env="SECRET_KEY"
    )
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Limites de tentativas de login
    MAX_LOGIN_ATTEMPTS: int = 5
    LOGIN_BLOCK_MINUTES: int = 30
    
    class Config:
        env_file = ".env"

# Instanciar as configurações
security_settings = SecuritySettings()

# Expor as configurações como variáveis do módulo
SECRET_KEY = security_settings.SECRET_KEY
ALGORITHM = security_settings.ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = security_settings.ACCESS_TOKEN_EXPIRE_MINUTES
REFRESH_TOKEN_EXPIRE_DAYS = security_settings.REFRESH_TOKEN_EXPIRE_DAYS
MAX_LOGIN_ATTEMPTS = security_settings.MAX_LOGIN_ATTEMPTS
LOGIN_BLOCK_MINUTES = security_settings.LOGIN_BLOCK_MINUTES

# Configurar o contexto de senha
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configurar OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifica se a senha está correta"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Gera hash seguro para a senha"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Cria um token JWT de acesso com expiração
    
    Args:
        data: Dados a serem codificados no token
        expires_delta: Tempo de expiração personalizado (opcional)
        
    Returns:
        Token JWT codificado
    """
    to_encode = data.copy()
    
    # Definir expiração
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=security_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Adicionar tempo de expiração e timestamp de criação
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access"
    })
    
    # Codificar o token
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            security_settings.SECRET_KEY, 
            algorithm=security_settings.ALGORITHM
        )
        logger.debug(f"Token criado para usuário {data.get('sub')}")
        return encoded_jwt
    except Exception as e:
        logger.error(f"Erro ao criar token: {str(e)}")
        raise

def create_refresh_token(data: dict) -> str:
    """
    Cria um token JWT de atualização com vida mais longa
    
    Args:
        data: Dados a serem codificados no token
        
    Returns:
        Token JWT de atualização
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=security_settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "refresh"
    })
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            security_settings.SECRET_KEY, 
            algorithm=security_settings.ALGORITHM
        )
        logger.debug(f"Refresh token criado para usuário {data.get('sub')}")
        return encoded_jwt
    except Exception as e:
        logger.error(f"Erro ao criar refresh token: {str(e)}")
        raise

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> UserModel:
    """
    Verifica o token e retorna o usuário atual
    
    Args:
        token: Token JWT de autenticação
        db: Sessão do banco de dados
        
    Returns:
        Objeto do usuário autenticado
        
    Raises:
        HTTPException: Se o token for inválido ou o usuário não existir
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Decodificar o token
        payload = jwt.decode(
            token, 
            security_settings.SECRET_KEY, 
            algorithms=[security_settings.ALGORITHM]
        )
        
        # Extrair e verificar informações básicas
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        
        if username is None:
            logger.warning("Token sem 'sub' encontrado")
            raise credentials_exception
            
        if token_type != "access":
            logger.warning(f"Tipo de token inválido: {token_type}")
            raise credentials_exception
            
    except JWTError as e:
        logger.warning(f"Erro ao validar token: {str(e)}")
        raise credentials_exception
    
    # Buscar usuário no banco de dados
    user = db.query(UserModel).filter(UserModel.email == username).first()
    
    if user is None:
        logger.warning(f"Usuário do token não encontrado: {username}")
        raise credentials_exception
        
    logger.debug(f"Usuário autenticado: {user.email} (ID: {user.id})")
    return user

async def get_current_active_user(
    current_user: UserModel = Depends(get_current_user)
) -> UserModel:
    """
    Verifica se o usuário está ativo
    
    Args:
        current_user: Usuário obtido do token
        
    Returns:
        Usuário ativo
        
    Raises:
        HTTPException: Se o usuário estiver inativo
    """
    if not current_user.is_active:
        logger.warning(f"Tentativa de acesso com usuário inativo: {current_user.email}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Inactive user"
        )
    return current_user

def verify_csrf_token(request: Request, csrf_token: str) -> bool:
    """
    Verifica se o token CSRF é válido
    
    Args:
        request: Objeto da requisição
        csrf_token: Token CSRF a ser verificado
        
    Returns:
        True se o token for válido, False caso contrário
    """
    stored_token = request.session.get("csrf_token")
    if not stored_token or stored_token != csrf_token:
        logger.warning("Token CSRF inválido detectado")
        return False
    return True

# Função para validar a força da senha
def validate_password_strength(password: str) -> bool:
    """
    Verifica se a senha atende aos requisitos mínimos de segurança
    
    Args:
        password: Senha a ser verificada
        
    Returns:
        True se a senha for forte o suficiente, False caso contrário
    """
    # Verificar comprimento mínimo
    if len(password) < 8:
        return False
    
    # Verificar se contém pelo menos um número
    if not any(char.isdigit() for char in password):
        return False
    
    # Verificar se contém pelo menos uma letra maiúscula
    if not any(char.isupper() for char in password):
        return False
    
    # Verificar se contém pelo menos uma letra minúscula
    if not any(char.islower() for char in password):
        return False
    
    # Verificar se contém pelo menos um caractere especial
    if not any(char in "!@#$%^&*()-_=+[]{}|;:'\",.<>/?`~" for char in password):
        return False
    
    return True

# Função para sanitizar input de usuário para logs
def sanitize_log_input(input_str: str) -> str:
    """
    Sanitiza o input do usuário para evitar ataques de log injection
    
    Args:
        input_str: String a ser sanitizada
        
    Returns:
        String sanitizada
    """
    if not isinstance(input_str, str):
        return str(input_str)
    
    # Remover caracteres de controle e quebras de linha
    return input_str.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')