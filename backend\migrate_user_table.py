#!/usr/bin/env python3
"""
Script para migrar a tabela users para suportar Google OAuth
"""

import sqlite3
import os
import sys

def migrate_user_table():
    """Adiciona colunas necessárias para Google OAuth na tabela users"""
    
    # Caminho para o banco de dados
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "finance_app.db")
    
    print(f"Migrando banco de dados: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Lista de colunas para adicionar
        columns_to_add = [
            ("username", "VARCHAR"),
            ("full_name", "VARCHAR"),
            ("is_verified", "BOOLEAN DEFAULT 0"),
            ("google_id", "VARCHAR"),
            ("profile_picture", "VARCHAR"),
            ("created_at", "DATETIME"),
            ("updated_at", "DATETIME")
        ]
        
        # Verificar quais colunas já existem
        cursor.execute("PRAGMA table_info(users)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        print(f"Colunas existentes: {existing_columns}")
        
        # Adicionar colunas que não existem
        for column_name, column_type in columns_to_add:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE users ADD COLUMN {column_name} {column_type}"
                    print(f"Executando: {sql}")
                    cursor.execute(sql)
                    print(f"✅ Coluna {column_name} adicionada")
                except sqlite3.Error as e:
                    print(f"❌ Erro ao adicionar coluna {column_name}: {e}")
            else:
                print(f"⏭️ Coluna {column_name} já existe")
        
        # Criar índices únicos se necessário
        try:
            cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS ix_users_username ON users (username)")
            print("✅ Índice ix_users_username criado")
        except sqlite3.Error as e:
            print(f"❌ Erro ao criar índice username: {e}")
            
        try:
            cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS ix_users_google_id ON users (google_id)")
            print("✅ Índice ix_users_google_id criado")
        except sqlite3.Error as e:
            print(f"❌ Erro ao criar índice google_id: {e}")
        
        # Commit das mudanças
        conn.commit()
        print("✅ Migração concluída com sucesso!")
        
        # Verificar estrutura final
        cursor.execute("PRAGMA table_info(users)")
        final_columns = [row[1] for row in cursor.fetchall()]
        print(f"Colunas finais: {final_columns}")
        
    except sqlite3.Error as e:
        print(f"❌ Erro na migração: {e}")
        return False
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    success = migrate_user_table()
    if not success:
        sys.exit(1)
    print("\n🎉 Migração concluída! Agora o Google OAuth está pronto.")
