from fastapi import APIRouter, Depends, HTTPException, status, Request, Body, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, cast, Date
from typing import List, Dict, Any, Optional
from ..database import get_db
from ..models.financial import User, Transaction
from ..schemas.financial import UserCreate, User as UserSchema, TransactionCreate, Transaction as TransactionSchema, TransactionUpdate
from ..security import get_password_hash, get_current_active_user
from ..dependencies import get_current_user
import logging
import json
import traceback
from pydantic import ValidationError
from datetime import datetime
from ..services.transaction_classifier import classifier_service

# Configurar logger específico para este módulo
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/users/", response_model=UserSchema)
def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        username=user.username,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.get("/users/me", response_model=UserSchema)
def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user

@router.post("/transactions/", response_model=TransactionSchema)
async def create_transaction(
    request: Request,
    transaction: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cria uma nova transação financeira com validação e logs detalhados.
    """
    transaction_str = json.dumps(transaction, default=str)
    logger.info(f"Iniciando criação de transação para usuário {current_user.id}")
    logger.debug(f"Dados brutos recebidos: {transaction_str}")

    client_host = request.client.host if hasattr(request, 'client') else "unknown"
    logger.info(f"Requisição de: {client_host}, usuário: {current_user.id} ({current_user.email})")

    try:
        headers = {k: v for k, v in request.headers.items()
                  if k.lower() not in ('authorization', 'cookie')}
        logger.debug(f"Headers: {headers}")

        # Validação de campos obrigatórios
        required_fields = ['description', 'amount', 'type', 'category']
        missing_fields = [field for field in required_fields if field not in transaction]
        if missing_fields:
            error_msg = f"Campos obrigatórios ausentes: {missing_fields}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={"error": error_msg, "missing_fields": missing_fields}
            )

        # Validação e conversão do amount
        if 'amount' in transaction and not isinstance(transaction['amount'], (int, float)):
            try:
                transaction['amount'] = float(transaction['amount'])
                logger.info(f"Convertido amount '{transaction['amount']}' para float")
            except (ValueError, TypeError):
                error_msg = f"Campo 'amount' deve ser um número: {transaction['amount']}"
                logger.error(error_msg)
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={"error": error_msg, "field": "amount"}
                )

        # Validação do tipo
        valid_types = ['income', 'expense', 'investment']
        if transaction.get('type') not in valid_types:
            error_msg = f"Valor inválido para 'type': {transaction['type']}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={"error": error_msg, "field": "type"}
            )

        # Processamento da data
        if 'date' in transaction and transaction['date']:
            try:
                # Primeiro trata caso específico de formato ISO com Z (UTC)
                if isinstance(transaction['date'], str) and 'Z' in transaction['date']:
                    # Remover o Z e tratar como UTC
                    date_str = transaction['date'].replace('Z', '')
                    for fmt in ['%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S']:
                        try:
                            transaction['date'] = datetime.strptime(date_str, fmt)
                            break
                        except ValueError:
                            continue
                else:
                    # Formatos existentes
                    for fmt in ['%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%d']:
                        try:
                            transaction['date'] = datetime.strptime(transaction['date'], fmt)
                            break
                        except ValueError:
                            continue

                if isinstance(transaction['date'], str):
                    raise ValueError(f"Formato de data não reconhecido")
            except Exception as e:
                logger.error(f"Erro ao converter data: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={"error": "Formato de data inválido", "field": "date"}
                )
        else:
            transaction['date'] = datetime.now()

        # Subtype padrão
        transaction['subtype'] = transaction.get('subtype', 'other')

        # Validação final com Pydantic
        try:
            validated_data = TransactionCreate(**transaction)
        except ValidationError as e:
            error_details = [{"loc": err["loc"], "msg": err["msg"]} for err in e.errors()]
            logger.error(f"Erro de validação Pydantic: {error_details}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={"validation_error": error_details}
            )

        # Criação da transação
        db_transaction = Transaction(
            user_id=current_user.id,
            description=validated_data.description,
            amount=validated_data.amount,
            type=validated_data.type,
            category=validated_data.category,
            date=validated_data.date,
            subtype=validated_data.subtype,
            investment_id=validated_data.investment_id  # Incluir campo de investimento
        )

        try:
            db.add(db_transaction)
            db.commit()
            db.refresh(db_transaction)
            logger.info(f"Transação criada com sucesso: ID {db_transaction.id}")
            return db_transaction
        except Exception as e:
            db.rollback()
            logger.error(f"Erro ao salvar no banco: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"error": "Erro ao salvar transação"}
            )

    except HTTPException:
        raise
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Erro não tratado: {str(e)}\n{error_details}")
        error_logger = logging.getLogger('app.errors')
        error_logger.error(f"Erro ao criar transação: {str(e)}\n{error_details}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": str(e)}
        )

@router.get("/transactions/", response_model=List[TransactionSchema])
def read_transactions(
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    family_member_id: int = None,
    db: Session = Depends(get_db)
):
    # Buscar IDs dos membros da família vinculados
    from ..models.financial import FamilyLink
    family_links = db.query(FamilyLink).filter(
        ((FamilyLink.requester_id == current_user.id) | (FamilyLink.invited_id == current_user.id)) &
        (FamilyLink.status == "accepted")
    ).all()

    # Coletar IDs de todos os membros da família (incluindo o próprio usuário)
    family_member_ids = {current_user.id}
    for link in family_links:
        if link.requester_id == current_user.id:
            family_member_ids.add(link.invited_id)
        else:
            family_member_ids.add(link.requester_id)

    # Construir query base
    query = db.query(Transaction)

    # Aplicar filtros
    if family_member_id is not None:
        # Verificar se o membro solicitado faz parte da família
        if family_member_id in family_member_ids:
            query = query.filter(Transaction.user_id == family_member_id)
        else:
            # Se não faz parte da família, retornar apenas transações do próprio usuário
            query = query.filter(Transaction.user_id == current_user.id)
    else:
        # Se não especificado, retornar transações de todos os membros da família
        query = query.filter(Transaction.user_id.in_(family_member_ids))

    transactions = query.offset(skip).limit(limit).all()
    return transactions

@router.put("/transactions/{transaction_id}", response_model=TransactionSchema)
def update_transaction(
    transaction_id: int,
    transaction: TransactionUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    db_transaction = db.query(Transaction).filter(
        Transaction.id == transaction_id,
        Transaction.user_id == current_user.id
    ).first()

    if not db_transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )

    # Atualizar apenas os campos não nulos
    update_data = transaction.dict(exclude_unset=True, exclude_none=True)
    logger.info(f"Dados recebidos para atualização: {update_data}")

    # Salvar a descrição e categoria original antes da atualização
    original_description = db_transaction.description
    original_category = db_transaction.category
    original_type = db_transaction.type
    original_subtype = db_transaction.subtype

    # Atualizar os campos
    for field, value in update_data.items():
        setattr(db_transaction, field, value)

    try:
        # Salvar a transação no banco de dados
        db.commit()
        db.refresh(db_transaction)

        # Verificar se a categoria foi alterada
        if 'category' in update_data and update_data['category'] != original_category:
            logger.info(f"Categoria alterada para '{db_transaction.description}': de '{original_category}' para '{db_transaction.category}'")

            # Salvar a classificação personalizada
            classifier_service.add_classifier(
                description=original_description,
                category=db_transaction.category,
                transaction_type=db_transaction.type if 'type' in update_data else original_type,
                subtype=db_transaction.subtype if 'subtype' in update_data else original_subtype
            )
            logger.info(f"Classificação personalizada salva para '{original_description}'")

        return db_transaction
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao atualizar: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/transactions/{transaction_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    db_transaction = db.query(Transaction).filter(
        Transaction.id == transaction_id,
        Transaction.user_id == current_user.id
    ).first()

    if not db_transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )

    db.delete(db_transaction)
    db.commit()
    return None

@router.get("/transactions/categories")
async def get_categories(current_user: User = Depends(get_current_active_user)):
    """Retorna todas as categorias únicas usadas nas transações do usuário"""
    db = next(get_db())
    categories = db.query(Transaction.category)\
                  .filter(Transaction.user_id == current_user.id)\
                  .distinct()\
                  .all()
    return {
        "categories": [category[0] for category in categories if category[0]]
    }

@router.post("/transactions/bulk/", status_code=status.HTTP_201_CREATED)
async def create_bulk_transactions(
    request: Request,
    data: Dict[str, List[Dict[str, Any]]] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Processa múltiplas transações em uma única requisição

    Formato esperado do body:
    {
        "transactions": [
            {transaction1},
            {transaction2},
            ...
        ]
    }
    """
    if "transactions" not in data or not isinstance(data["transactions"], list):
        logger.error("Formato de dados inválido para criação em lote")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Formato de dados inválido. Esperado: {'transactions': [...]}", "field": "transactions"}
        )

    transactions = data["transactions"]
    logger.info(f"Recebido pedido de criação em lote com {len(transactions)} transações")

    # Verificação inicial
    if not transactions:
        return {"created": 0, "transactions": [], "error": None}

    created_transactions = []
    errors = []

    try:
        for transaction in transactions:
            try:
                # Validação de campos obrigatórios
                required_fields = ['description', 'amount', 'type', 'category']
                missing_fields = [field for field in required_fields if field not in transaction]
                if missing_fields:
                    errors.append({
                        "transaction": transaction,
                        "error": f"Campos obrigatórios ausentes: {missing_fields}"
                    })
                    continue

                # Validação e conversão do amount
                if 'amount' in transaction and not isinstance(transaction['amount'], (int, float)):
                    try:
                        transaction['amount'] = float(transaction['amount'])
                    except (ValueError, TypeError):
                        errors.append({
                            "transaction": transaction,
                            "error": f"Campo 'amount' deve ser um número: {transaction['amount']}"
                        })
                        continue

                # Validação do tipo
                valid_types = ['income', 'expense', 'investment']
                if transaction.get('type') not in valid_types:
                    errors.append({
                        "transaction": transaction,
                        "error": f"Valor inválido para 'type': {transaction['type']}"
                    })
                    continue

                # Processamento da data
                if 'date' in transaction and transaction['date']:
                    try:
                        # Tratar diferentes formatos de data
                        if isinstance(transaction['date'], str):
                            # Tratamento de ISO com Z (UTC)
                            if 'Z' in transaction['date']:
                                date_str = transaction['date'].replace('Z', '')
                                for fmt in ['%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S']:
                                    try:
                                        transaction['date'] = datetime.strptime(date_str, fmt)
                                        break
                                    except ValueError:
                                        continue
                            else:
                                # Formatos existentes
                                for fmt in ['%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%d']:
                                    try:
                                        transaction['date'] = datetime.strptime(transaction['date'], fmt)
                                        break
                                    except ValueError:
                                        continue

                            # Se ainda for string, é formato inválido
                            if isinstance(transaction['date'], str):
                                raise ValueError(f"Formato de data não reconhecido")
                    except Exception as e:
                        errors.append({
                            "transaction": transaction,
                            "error": f"Formato de data inválido: {str(e)}"
                        })
                        continue
                else:
                    transaction['date'] = datetime.now()

                # Subtype padrão
                transaction['subtype'] = transaction.get('subtype', 'other')

                # Verificar se já existe uma transação idêntica (mesma descrição, valor, data e tipo)
                existing_transaction = db.query(Transaction).filter(
                    Transaction.user_id == current_user.id,
                    Transaction.description == transaction['description'],
                    Transaction.amount == transaction['amount'],
                    Transaction.date == transaction['date'],
                    Transaction.type == transaction['type']
                ).first()

                if existing_transaction:
                    logger.warning(f"Transação duplicada detectada: {transaction['description']}, {transaction['amount']}, {transaction['date']}")
                    # Não criar a transação duplicada
                    continue

                # Criar a transação apenas se não for duplicada
                db_transaction = Transaction(
                    user_id=current_user.id,
                    description=transaction['description'],
                    amount=transaction['amount'],
                    type=transaction['type'],
                    category=transaction['category'],
                    date=transaction['date'],
                    subtype=transaction['subtype'],
                    investment_id=transaction.get('investment_id')  # Incluir campo de investimento
                )

                db.add(db_transaction)
                created_transactions.append(db_transaction)

            except Exception as e:
                errors.append({
                    "transaction": transaction,
                    "error": str(e)
                })

        if created_transactions:
            db.commit()

            # Refresh para obter IDs
            for trans in created_transactions:
                db.refresh(trans)

            logger.info(f"Criadas com sucesso {len(created_transactions)} transações em lote")
        else:
            logger.warning("Nenhuma transação válida para criar em lote")

    except Exception as e:
        db.rollback()
        error_details = traceback.format_exc()
        logger.error(f"Erro ao processar transações em lote: {str(e)}\n{error_details}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": f"Erro ao processar lote: {str(e)}"}
        )

    # Preparar resposta
    response_data = {
        "created": len(created_transactions),
        "transactions": [
            {
                "id": trans.id,
                "description": trans.description,
                "amount": trans.amount,
                "type": trans.type,
                "category": trans.category,
                "date": trans.date.isoformat(),
                "subtype": trans.subtype
            }
            for trans in created_transactions
        ],
        "errors": errors if errors else None
    }

    if errors:
        logger.warning(f"Criação em lote concluída com {len(errors)} erros")

    return response_data


@router.get("/transactions/summary")
def get_transactions_summary(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Retorna um resumo das transações filtradas por data e tipo.
    """
    # Construir query base
    query = db.query(Transaction).filter(Transaction.user_id == current_user.id)

    # Aplicar filtros de data
    if start_date:
        query = query.filter(cast(Transaction.date, Date) >= start_date.date())

    if end_date:
        query = query.filter(cast(Transaction.date, Date) <= end_date.date())

    # Aplicar filtro de tipo
    if type:
        query = query.filter(Transaction.type == type)

    # Calcular totais
    total_income = db.query(func.sum(Transaction.amount)).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "income"
    )

    total_expense = db.query(func.sum(Transaction.amount)).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "expense",
        Transaction.subtype != "credit_card_purchase",
        Transaction.subtype != "refund"
    )

    total_investment = db.query(func.sum(Transaction.amount)).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "investment"
    )

    # Aplicar filtros de data aos totais
    if start_date:
        total_income = total_income.filter(cast(Transaction.date, Date) >= start_date.date())
        total_expense = total_expense.filter(cast(Transaction.date, Date) >= start_date.date())
        total_investment = total_investment.filter(cast(Transaction.date, Date) >= start_date.date())

    if end_date:
        total_income = total_income.filter(cast(Transaction.date, Date) <= end_date.date())
        total_expense = total_expense.filter(cast(Transaction.date, Date) <= end_date.date())
        total_investment = total_investment.filter(cast(Transaction.date, Date) <= end_date.date())

    # Executar queries
    income_sum = total_income.scalar() or 0.0
    expense_sum = total_expense.scalar() or 0.0
    investment_sum = total_investment.scalar() or 0.0

    # Obter transações para contagem
    transactions = query.all()

    return {
        "period": {
            "start_date": start_date.date() if start_date else None,
            "end_date": end_date.date() if end_date else None,
            "type_filter": type
        },
        "summary": {
            "total_income": float(income_sum),
            "total_expense": float(expense_sum),
            "total_investment": float(investment_sum),
            "net_balance": float(income_sum - expense_sum),
            "transaction_count": len(transactions)
        }
    }
