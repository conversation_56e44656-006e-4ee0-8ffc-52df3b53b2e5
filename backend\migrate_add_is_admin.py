#!/usr/bin/env python3
"""
Migração para adicionar o campo is_admin à tabela users
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.security import get_password_hash
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_is_admin():
    """Adiciona o campo is_admin à tabela users"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # Verificar se a coluna já existe
            result = connection.execute(text("PRAGMA table_info(users)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'is_admin' in columns:
                logger.info("✅ Campo is_admin já existe na tabela users")
                return True
            
            # Adicionar a coluna is_admin
            logger.info("Adicionando campo is_admin à tabela users...")
            connection.execute(text("ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE"))
            connection.commit()
            
            # Definir o usuário <EMAIL> como admin
            logger.info("Definindo <EMAIL> como administrador...")
            connection.execute(text(
                "UPDATE users SET is_admin = TRUE WHERE email = '<EMAIL>'"
            ))
            connection.commit()
            
            logger.info("✅ Migração concluída com sucesso!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro na migração: {str(e)}")
        return False

if __name__ == "__main__":
    success = migrate_add_is_admin()
    if success:
        print("✅ Migração executada com sucesso!")
        sys.exit(0)
    else:
        print("❌ Falha na migração!")
        sys.exit(1)
