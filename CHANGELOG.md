# Changelog

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

O formato é baseado em [Keep a Changelog](https://keepachangelog.com/pt-BR/1.0.0/),
e este projeto adere ao [Semantic Versioning](https://semver.org/lang/pt-BR/).

## [2.0.2] - 2024-12-05

### 🐛 Corrigido

#### Fluxo de Login com Google
- **Redirecionamento Correto**: Implementado redirecionamento automático após login Google
- **UX Melhorada**: Token não é mais exibido diretamente ao usuário
- **Fluxo Automático**: Backend redireciona para frontend com token via query params
- **Segurança Mantida**: Token transmitido de forma segura entre backend e frontend

#### Relacionamentos SQLAlchemy
- **Multiple Foreign Keys**: Corrigido erro de múltiplos caminhos de foreign key entre User e Transaction
- **Especificação Clara**: Definido foreign_keys explicitamente para relacionamentos
- **Login Funcional**: Google OAuth agora funciona sem erros de relacionamento

### 🔧 Alterado

#### Fluxo de Autenticação
- **Backend Callback**: Modificado para redirecionar em vez de retornar JSON
- **Frontend Callback**: Atualizado para processar token via query parameters
- **Rota Simplificada**: Mudança de `/auth/google/callback` para `/auth/callback`
- **Compatibilidade**: Mantido fallback para fluxo antigo

## [2.0.1] - 2024-12-05

### 🐛 Corrigido

#### Problemas de Importação no Backend
- **Dashboard Router**: Corrigidas importações de modelos inexistentes (Category, RecurringTransaction, CreditCard, CreditCardStatement)
- **Funcionalidades Simplificadas**: Temporariamente simplificadas funcionalidades que dependem de modelos não implementados
- **Compatibilidade**: Mantida estrutura de resposta para compatibilidade futura
- **Comentários**: Adicionados comentários indicando funcionalidades futuras

#### Backend Funcional
- **Servidor Inicia**: Backend agora inicia sem erros de importação
- **Endpoints Básicos**: Endpoints principais funcionando corretamente
- **Estrutura Preparada**: Código preparado para futuras implementações

### 📝 Funcionalidades Temporariamente Simplificadas
- **Gestão de Cartões de Crédito**: Retorna dados vazios (implementação futura)
- **Transações Recorrentes**: Retorna dados vazios (implementação futura)
- **Faturas de Cartão**: Retorna dados vazios (implementação futura)

## [2.0.0] - 2024-12-05

### 🚀 Adicionado

#### Sistema de Filtros Globais
- **Componente GlobalFilters**: Sistema de filtros unificado com data, tipo, categoria e membro da família
- **Contexto FiltersContext**: Gerenciamento global de estado dos filtros
- **Persistência**: Filtros salvos no localStorage e mantidos entre sessões
- **Sincronização**: Filtros aplicados automaticamente em todas as páginas
- **Interface Intuitiva**: Chips visuais para filtros ativos com opção de remoção individual

#### Navegação Colapsável e Páginas Separadas
- **CollapsibleNavBar**: Barra de navegação responsiva que se esconde ao fazer scroll
- **Ícone Flutuante**: Botão flutuante que aparece durante o scroll para acesso rápido
- **Páginas Dedicadas**: Separação em páginas independentes (Analysis, Planning, Documents, Family)
- **Animações Suaves**: Transições fluidas entre estados colapsado/expandido
- **Responsividade**: Adaptação automática para diferentes tamanhos de tela

#### Sistema de Propriedade de Transações
- **Campo owner_id**: Novo campo no modelo Transaction para identificar o dono real
- **Coluna Dono**: Nova coluna na tabela de transações mostrando o proprietário
- **Seleção de Dono**: Campo no formulário para escolher o dono da transação
- **Permissões Familiares**: Capacidade de criar transações em nome de outros membros
- **Migração Automática**: Sistema de migração com compatibilidade retroativa

#### Melhorias de Interface
- **Design Moderno**: Interface atualizada com Material-UI mais recente
- **Filtros Visuais**: Representação visual dos filtros ativos
- **Navegação Intuitiva**: Menu de navegação mais acessível e organizado
- **Feedback Visual**: Indicadores visuais para ações do usuário

### 🔧 Alterado

#### Backend
- **Endpoints Atualizados**: Suporte a filtros avançados em todos os endpoints de transações
- **Modelo Transaction**: Adição do campo `owner_id` com foreign key para users
- **Schemas Pydantic**: Atualização para incluir owner_id em criação e listagem
- **Filtros de Query**: Implementação de filtros por data, tipo, categoria e membro da família

#### Frontend
- **Arquitetura de Páginas**: Migração de sistema de abas para páginas separadas
- **Contextos React**: Implementação de contextos para gerenciamento de estado global
- **Componentes Reutilizáveis**: Criação de componentes modulares e reutilizáveis
- **Roteamento**: Atualização do sistema de rotas para suportar páginas independentes

#### Banco de Dados
- **Migração owner_id**: Nova migração usando batch mode para compatibilidade com SQLite
- **Relacionamentos**: Novos relacionamentos entre Transaction e User para propriedade
- **Índices**: Otimização de consultas com índices apropriados

### 🐛 Corrigido

#### Problemas de Login e Autenticação
- **Google OAuth**: Correção do redirect_uri para usar backend (127.0.0.1:8000)
- **Rotas de Dashboard**: Inclusão do router dashboard no main.py para corrigir erro 405
- **Endpoints de Summary**: Implementação do endpoint /transactions/summary para planejamento

#### Problemas de Navegação
- **Página de Administração**: Adição de AppBar e menu de navegação na página de usuários
- **Navegação de Retorno**: Possibilidade de voltar ao dashboard a partir de qualquer página
- **Layout Consistente**: Padronização do layout em todas as páginas

#### Problemas de Migração
- **SQLite Constraints**: Uso de batch mode para foreign keys em SQLite
- **Múltiplas Heads**: Resolução de conflitos de migração com merge automático
- **Compatibilidade**: Garantia de compatibilidade retroativa com dados existentes

### 🔒 Segurança

#### Melhorias de Autenticação
- **Validação de Permissões**: Verificação de permissões para criação de transações em nome de outros
- **Isolamento de Dados**: Garantia de que usuários só acessem dados autorizados
- **Auditoria**: Log de ações para rastreabilidade

#### Privacidade
- **Dados Locais**: Processamento de IA mantido local sem envio para nuvem
- **Filtros Seguros**: Validação de filtros no backend para prevenir ataques
- **Sanitização**: Limpeza de dados de entrada para prevenir XSS

### 📈 Performance

#### Otimizações de Frontend
- **Lazy Loading**: Carregamento sob demanda de componentes pesados
- **Memoização**: Uso de React.memo e useMemo para otimizar re-renders
- **Filtros Eficientes**: Implementação otimizada de filtros com debounce

#### Otimizações de Backend
- **Consultas Otimizadas**: Uso de joins eficientes para reduzir consultas ao banco
- **Índices de Banco**: Criação de índices para consultas frequentes
- **Cache de Filtros**: Sistema de cache para filtros utilizados frequentemente

### 🧪 Testes

#### Cobertura de Testes
- **Testes de Componentes**: Testes unitários para novos componentes React
- **Testes de API**: Testes de integração para novos endpoints
- **Testes de Migração**: Validação de migrações de banco de dados

#### Qualidade de Código
- **Linting**: Configuração de ESLint e Prettier para frontend
- **Type Safety**: Melhoria da tipagem TypeScript
- **Documentação**: Atualização completa da documentação

### 📚 Documentação

#### README Atualizado
- **Arquitetura Completa**: Documentação detalhada da estrutura do projeto
- **Guias de Instalação**: Instruções passo a passo para configuração
- **Exemplos de Uso**: Casos de uso práticos e exemplos de código

#### Documentação Técnica
- **API Documentation**: Documentação automática com FastAPI
- **Componentes**: Documentação de componentes React com Storybook
- **Deployment**: Guias de deploy para produção

## [1.5.0] - 2024-11-15

### Adicionado
- Sistema de gestão familiar com convites
- Processamento inteligente de documentos
- Integração com IA local (Ollama/Llama3)
- Sistema de metas e orçamentos
- Análises financeiras avançadas

### Alterado
- Interface do usuário modernizada
- Performance melhorada
- Sistema de autenticação aprimorado

### Corrigido
- Problemas de sincronização de dados
- Bugs de categorização automática
- Questões de segurança menores

## [1.0.0] - 2024-10-01

### Adicionado
- Sistema básico de transações
- Autenticação com JWT
- Dashboard inicial
- Categorização manual
- Relatórios básicos

---

## Tipos de Mudanças

- **Adicionado** para novas funcionalidades
- **Alterado** para mudanças em funcionalidades existentes
- **Descontinuado** para funcionalidades que serão removidas
- **Removido** para funcionalidades removidas
- **Corrigido** para correções de bugs
- **Segurança** para vulnerabilidades corrigidas
