import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import _ from 'lodash';
import { Paper, Typography, Grid, Box } from '@mui/material';

interface Transaction {
  id: number;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: string;
  subtype?: string;
}

interface MonthlyData {
  month: string;
  income: number;
  expenses: number;
  investments: number;
  balance: number;
}

interface CategoryData {
  name: string;
  value: number;
}

interface GeneralAnalyticsProps {
  transactions: Transaction[];
}

const GeneralAnalytics: React.FC<GeneralAnalyticsProps> = ({ transactions }) => {
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

  useEffect(() => {
    // Filtrar transações excluindo compras no cartão e reembolsos (seguindo a mesma lógica do Dashboard)
    const filteredTransactions = transactions.filter(transaction =>
      !(transaction.type === 'expense' && transaction.subtype === 'credit_card_purchase') &&
      !(transaction.type === 'expense' && transaction.subtype === 'refund')
    );

    // Agrupar transações por mês
    const groupedByMonth = _.groupBy(filteredTransactions, (t) => {
      const date = new Date(t.date);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    });

    // Preparar dados para o gráfico de linha
    const monthlyAnalytics: MonthlyData[] = Object.entries(groupedByMonth).map(([month, trans]) => {
      const income = trans
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      const expenses = trans
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);
      const investments = trans
        .filter(t => t.type === 'investment')
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        month,
        income,
        expenses,
        investments,
        balance: income - expenses - investments
      };
    });

    // Agrupar por categoria para o gráfico de pizza (apenas despesas filtradas)
    const expenseTransactions = filteredTransactions.filter(t => t.type === 'expense');
    const groupedByCategory = _.groupBy(expenseTransactions, 'category');
    const categoryAnalytics: CategoryData[] = Object.entries(groupedByCategory).map(([category, trans]) => ({
      name: category,
      value: trans.reduce((sum, t) => sum + t.amount, 0)
    }));

    setMonthlyData(_.sortBy(monthlyAnalytics, 'month'));
    setCategoryData(categoryAnalytics);
  }, [transactions]);

  return (
    <Box sx={{ width: '100%' }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Evolução Mensal
            </Typography>
            <Box sx={{ height: 400 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value: number) => [
                      new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(value),
                      ''
                    ]}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="income" stroke="#00C49F" name="Receitas" strokeWidth={2} />
                  <Line type="monotone" dataKey="expenses" stroke="#FF8042" name="Gastos" strokeWidth={2} />
                  <Line type="monotone" dataKey="investments" stroke="#8884d8" name="Investimentos" strokeWidth={2} />
                  <Line type="monotone" dataKey="balance" stroke="#0088FE" name="Saldo" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Distribuição por Categoria (Despesas)
            </Typography>
            <Box sx={{ height: 400 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({name, percent}) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  >
                    {categoryData.map((_entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [
                      new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(value),
                      'Valor'
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Resumo por Categoria
            </Typography>
            <Grid container spacing={2}>
              {categoryData.map((category) => (
                <Grid item xs={12} sm={6} key={category.name}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {category.name}
                      </Typography>
                      <Typography variant="body1">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(category.value)}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GeneralAnalytics;
