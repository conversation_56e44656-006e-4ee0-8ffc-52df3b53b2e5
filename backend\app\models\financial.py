from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, Float, Date, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)

    # Campos para Google OAuth e administração
    username = Column(String, unique=True, index=True, nullable=True)
    full_name = Column(String, nullable=True)
    is_verified = Column(Boolean, default=False)
    google_id = Column(String, unique=True, index=True, nullable=True)
    profile_picture = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True)

    accounts = relationship("Account", back_populates="owner")
    transactions = relationship("Transaction", back_populates="user")
    scheduled_transactions = relationship("ScheduledTransaction", back_populates="user")
    financial_goals = relationship("FinancialGoal", back_populates="user")
    investments = relationship("Investment", back_populates="user")


class Account(Base):
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)
    account_type = Column(String)
    balance = Column(Float, default=0.0)
    currency = Column(String, default="BRL")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))

    owner = relationship("User", back_populates="accounts")
    transactions = relationship("Transaction", back_populates="account")


class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True)
    description = Column(String)
    amount = Column(Float)
    date = Column(Date, index=True)
    type = Column(String, index=True)  # 'income', 'expense', 'transfer', 'investment'
    category = Column(String, index=True)
    subtype = Column(String, nullable=True)  # Pode armazenar subcategorias ou tags
    notes = Column(Text, nullable=True)

    # Novos campos para rastreamento de investimentos
    investment_id = Column(Integer, ForeignKey("investments.id"), nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Dono real da transação
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)

    user = relationship("User", back_populates="transactions", foreign_keys=[user_id])
    owner = relationship("User", foreign_keys=[owner_id])  # Relacionamento com o dono
    account = relationship("Account", back_populates="transactions")
    investment = relationship("Investment", back_populates="transactions")


class ScheduledTransaction(Base):
    __tablename__ = "scheduled_transactions"

    id = Column(Integer, primary_key=True, index=True)
    description = Column(String)
    amount = Column(Float)
    start_date = Column(Date)
    end_date = Column(Date, nullable=True)
    frequency = Column(String)  # 'daily', 'weekly', 'monthly', 'yearly'
    interval = Column(Integer, default=1)  # A cada X dias/semanas/meses
    type = Column(String)  # 'income', 'expense'
    category = Column(String)
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    last_execution = Column(Date, nullable=True)
    next_execution = Column(Date, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)

    user = relationship("User", back_populates="scheduled_transactions")


class FinancialGoal(Base):
    __tablename__ = "financial_goals"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    description = Column(Text, nullable=True)
    target_amount = Column(Float)
    current_amount = Column(Float, default=0.0)
    start_date = Column(Date)
    target_date = Column(Date)
    completed = Column(Boolean, default=False)
    completion_date = Column(Date, nullable=True)
    category = Column(String, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))

    user = relationship("User", back_populates="financial_goals")
    contributions = relationship("Contribution", back_populates="goal")


class Contribution(Base):
    __tablename__ = "contributions"

    id = Column(Integer, primary_key=True, index=True)
    amount = Column(Float)
    date = Column(Date, default=datetime.now)
    description = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    goal_id = Column(Integer, ForeignKey("financial_goals.id"))

    goal = relationship("FinancialGoal", back_populates="contributions")


class Investment(Base):
    __tablename__ = "investments"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    description = Column(Text, nullable=True)
    type = Column(String)  # savings, stocks, bonds, funds, crypto, real_estate, other
    initial_amount = Column(Float)
    current_amount = Column(Float)
    interest_rate = Column(Float, nullable=True)
    start_date = Column(Date)
    end_date = Column(Date, nullable=True)
    institution = Column(String, nullable=True)
    investment_strategy = Column(String, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))

    user = relationship("User", back_populates="investments")
    transactions = relationship("Transaction", back_populates="investment")


class Budget(Base):
    __tablename__ = "budgets"

    id = Column(Integer, primary_key=True, index=True)
    category = Column(String, index=True)
    monthly_limit = Column(Float)
    start_date = Column(Date, default=datetime.now)
    end_date = Column(Date, nullable=True)
    notes = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))

    user = relationship("User", backref="budgets")


class FamilyLink(Base):
    __tablename__ = "family_links"

    id = Column(Integer, primary_key=True, index=True)
    requester_id = Column(Integer, ForeignKey("users.id"))  # Quem enviou o convite
    invited_id = Column(Integer, ForeignKey("users.id"))    # Quem foi convidado
    status = Column(String, default="pending")  # pending, accepted, rejected
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relacionamentos
    requester = relationship("User", foreign_keys=[requester_id], backref="sent_family_invites")
    invited = relationship("User", foreign_keys=[invited_id], backref="received_family_invites")