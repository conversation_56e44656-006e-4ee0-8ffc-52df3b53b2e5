-- Add recent transactions for AI testing
INSERT INTO transactions (user_id, description, amount, type, category, date, subtype) VALUES
(1, 'Supermercado Extra', 250.50, 'expense', 'Alimentação', '2025-05-20', 'other'),
(1, 'Posto Shell', 120.00, 'expense', 'Transporte', '2025-05-19', 'other'),
(1, 'Netflix', 29.90, 'expense', 'Lazer', '2025-05-18', 'other'),
(1, 'Farmácia Drogasil', 45.80, 'expense', 'Saúde', '2025-05-17', 'other'),
(1, 'Aluguel', 1200.00, 'expense', 'Moradia', '2025-05-16', 'other'),
(1, '<PERSON><PERSON> de Luz', 180.00, 'expense', 'Moradia', '2025-05-15', 'other'),
(1, 'Restaurante', 85.00, 'expense', 'Alimentação', '2025-05-14', 'other'),
(1, 'Uber', 25.50, 'expense', 'Transporte', '2025-05-13', 'other'),
(1, 'Cinema', 40.00, 'expense', 'Lazer', '2025-05-12', 'other'),
(1, '<PERSON><PERSON><PERSON> Carr<PERSON>', 180.30, 'expense', '<PERSON>menta<PERSON>', '2025-05-11', 'other'),
(1, 'Academia', 89.90, 'expense', 'Saúde', '2025-05-10', 'other'),
(1, 'Spotify', 19.90, 'expense', 'Lazer', '2025-05-09', 'other'),
(1, 'Conta de Internet', 99.90, 'expense', 'Moradia', '2025-05-08', 'other'),
(1, 'Loja de Roupas', 150.00, 'expense', 'Vestuário', '2025-05-07', 'other'),
(1, 'Padaria', 35.00, 'expense', 'Alimentação', '2025-05-06', 'other'),
(1, 'Consulta Médica', 200.00, 'expense', 'Saúde', '2025-05-05', 'other'),
(1, 'Livros', 75.00, 'expense', 'Educação', '2025-05-04', 'other'),
(1, 'Gasolina', 180.00, 'expense', 'Transporte', '2025-05-03', 'other'),
(1, 'Supermercado Pão de Açúcar', 320.00, 'expense', 'Alimentação', '2025-05-02', 'other'),
(1, 'Farmácia São Paulo', 65.00, 'expense', 'Saúde', '2025-05-01', 'other');
