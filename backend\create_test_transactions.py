#!/usr/bin/env python3

import sys
import os
from datetime import datetime, timedelta
import random

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models.financial import User, Transaction

def create_test_transactions():
    print("🔧 CREATING TEST TRANSACTIONS FOR AI ANALYSIS")
    print("=" * 50)
    
    try:
        # Get database session
        db = next(get_db())
        
        # Get admin user
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            print("❌ Admin user not found")
            return
        
        print(f"✅ Found admin user: {admin_user.email}")
        
        # Check existing transactions
        existing_count = db.query(Transaction).filter(Transaction.user_id == admin_user.id).count()
        print(f"📊 Existing transactions: {existing_count}")
        
        if existing_count >= 15:
            print("✅ Sufficient transactions already exist!")
            return
        
        # Sample transaction data for testing AI
        sample_transactions = [
            {"description": "Supermercado Extra", "amount": 250.50, "type": "expense", "category": "Alimentação"},
            {"description": "Posto Shell", "amount": 120.00, "type": "expense", "category": "Transporte"},
            {"description": "Netflix", "amount": 29.90, "type": "expense", "category": "Lazer"},
            {"description": "Farmácia Drogasil", "amount": 45.80, "type": "expense", "category": "Saúde"},
            {"description": "Aluguel", "amount": 1200.00, "type": "expense", "category": "Moradia"},
            {"description": "Conta de Luz", "amount": 180.00, "type": "expense", "category": "Moradia"},
            {"description": "Restaurante", "amount": 85.00, "type": "expense", "category": "Alimentação"},
            {"description": "Uber", "amount": 25.50, "type": "expense", "category": "Transporte"},
            {"description": "Cinema", "amount": 40.00, "type": "expense", "category": "Lazer"},
            {"description": "Salário", "amount": 5000.00, "type": "income", "category": "Salário"},
            {"description": "Freelance", "amount": 800.00, "type": "income", "category": "Trabalho Extra"},
            {"description": "Mercado Carrefour", "amount": 180.30, "type": "expense", "category": "Alimentação"},
            {"description": "Academia", "amount": 89.90, "type": "expense", "category": "Saúde"},
            {"description": "Spotify", "amount": 19.90, "type": "expense", "category": "Lazer"},
            {"description": "Conta de Internet", "amount": 99.90, "type": "expense", "category": "Moradia"},
            {"description": "Investimento CDB", "amount": 1000.00, "type": "investment", "category": "Investimentos"},
            {"description": "Loja de Roupas", "amount": 150.00, "type": "expense", "category": "Vestuário"},
            {"description": "Padaria", "amount": 35.00, "type": "expense", "category": "Alimentação"},
            {"description": "Consulta Médica", "amount": 200.00, "type": "expense", "category": "Saúde"},
            {"description": "Livros", "amount": 75.00, "type": "expense", "category": "Educação"},
        ]
        
        # Create transactions with dates spread over the last 3 months
        base_date = datetime.now() - timedelta(days=90)
        created_count = 0
        
        for i, trans_data in enumerate(sample_transactions):
            # Random date within the last 3 months
            random_days = random.randint(0, 90)
            trans_date = base_date + timedelta(days=random_days)
            
            transaction = Transaction(
                user_id=admin_user.id,
                description=trans_data["description"],
                amount=trans_data["amount"],
                type=trans_data["type"],
                category=trans_data["category"],
                date=trans_date,
                subtype="other"
            )
            
            db.add(transaction)
            created_count += 1
        
        # Commit all transactions
        db.commit()
        print(f"✅ Created {created_count} test transactions")
        
        # Verify total count
        total_count = db.query(Transaction).filter(Transaction.user_id == admin_user.id).count()
        print(f"📊 Total transactions now: {total_count}")
        
        if total_count >= 10:
            print("🎉 Sufficient transactions for AI analysis!")
        
        print("\n" + "=" * 50)
        print("🎯 TEST TRANSACTIONS CREATED SUCCESSFULLY!")
        
    except Exception as e:
        print(f"❌ Error creating transactions: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_test_transactions()
