import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Divider,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Google as GoogleIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Verified as VerifiedIcon,
  Security as SecurityIcon,
  Link as LinkIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import api from '../services/api';

interface User {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  is_active: boolean;
  is_verified?: boolean;
  is_admin?: boolean;
  google_id?: string;
  profile_picture?: string;
  created_at?: string;
}

interface CreateUserData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentTab, setCurrentTab] = useState(0);

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [googleOAuthStatus, setGoogleOAuthStatus] = useState<any>(null);

  // Form data
  const [newUser, setNewUser] = useState<CreateUserData>({
    email: '',
    password: '',
    first_name: '',
    last_name: ''
  });

  useEffect(() => {
    loadUsers();
    checkGoogleOAuthStatus();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/users');
      setUsers(response.data);
      setError(null);
    } catch (err: any) {
      setError('Erro ao carregar usuários: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  const checkGoogleOAuthStatus = async () => {
    try {
      const response = await api.get('/auth/google/status');
      setGoogleOAuthStatus(response.data);
    } catch (err) {
      console.error('Erro ao verificar status do Google OAuth:', err);
    }
  };

  const handleCreateUser = async () => {
    try {
      if (!newUser.email || !newUser.password || !newUser.first_name || !newUser.last_name) {
        setError('Todos os campos são obrigatórios');
        return;
      }

      await api.post('/admin/users', newUser);
      setSuccess('Usuário criado com sucesso!');
      setCreateDialogOpen(false);
      setNewUser({ email: '', password: '', first_name: '', last_name: '' });
      loadUsers();
    } catch (err: any) {
      setError('Erro ao criar usuário: ' + (err.response?.data?.detail || err.message));
    }
  };

  const handleToggleUserStatus = async (userId: number) => {
    try {
      await api.put(`/admin/users/${userId}/activate`);
      setSuccess('Status do usuário alterado com sucesso!');
      loadUsers();
    } catch (err: any) {
      setError('Erro ao alterar status: ' + (err.response?.data?.detail || err.message));
    }
  };

  const handleToggleAdminStatus = async (userId: number) => {
    try {
      await api.put(`/admin/users/${userId}/toggle-admin`);
      setSuccess('Status de administrador alterado com sucesso!');
      loadUsers();
    } catch (err: any) {
      setError('Erro ao alterar status admin: ' + (err.response?.data?.detail || err.message));
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Tem certeza que deseja deletar este usuário?')) {
      return;
    }

    try {
      await api.delete(`/admin/users/${userId}`);
      setSuccess('Usuário deletado com sucesso!');
      loadUsers();
    } catch (err: any) {
      setError('Erro ao deletar usuário: ' + (err.response?.data?.detail || err.message));
    }
  };

  const handleGoogleLogin = async () => {
    try {
      const response = await api.get('/auth/google/login');
      if (response.data.auth_url) {
        window.open(response.data.auth_url, '_blank');
        setSuccess('Abra a nova aba para fazer login com Google');
      }
    } catch (err: any) {
      setError('Erro ao iniciar login Google: ' + (err.response?.data?.detail || err.message));
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getUserDisplayName = (user: User) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.username || user.email.split('@')[0];
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Administração do Sistema
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Usuários" icon={<PersonIcon />} />
          <Tab label="Configurações" icon={<SettingsIcon />} />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <>
          {/* Status do Google OAuth */}
          {googleOAuthStatus && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Status do Google OAuth
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Chip
                  icon={<GoogleIcon />}
                  label={googleOAuthStatus.google_oauth_enabled ? 'Habilitado' : 'Desabilitado'}
                  color={googleOAuthStatus.google_oauth_enabled ? 'success' : 'error'}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                {googleOAuthStatus.google_oauth_enabled && (
                  <Button
                    variant="outlined"
                    startIcon={<GoogleIcon />}
                    onClick={handleGoogleLogin}
                    size="small"
                  >
                    Testar Login Google
                  </Button>
                )}
              </Grid>
            </Grid>
            {!googleOAuthStatus.google_oauth_enabled && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Para habilitar o Google OAuth, configure as variáveis GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Criar Novo Usuário
        </Button>
      </Box>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Usuário</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Admin</TableCell>
              <TableCell>Criado em</TableCell>
              <TableCell>Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  Carregando...
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  Nenhum usuário encontrado
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {user.profile_picture ? (
                        <img
                          src={user.profile_picture}
                          alt="Profile"
                          style={{ width: 32, height: 32, borderRadius: '50%' }}
                        />
                      ) : (
                        <PersonIcon />
                      )}
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {getUserDisplayName(user)}
                        </Typography>
                        {user.username && (
                          <Typography variant="caption" color="text.secondary">
                            @{user.username}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Chip
                        size="small"
                        label={user.is_active ? 'Ativo' : 'Inativo'}
                        color={user.is_active ? 'success' : 'error'}
                      />
                      {user.is_verified && (
                        <Chip
                          size="small"
                          icon={<VerifiedIcon />}
                          label="Verificado"
                          color="info"
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {user.email === '<EMAIL>' && (
                        <Chip
                          size="small"
                          icon={<AdminIcon />}
                          label="Admin"
                          color="primary"
                        />
                      )}
                      {user.google_id && (
                        <Chip
                          size="small"
                          icon={<GoogleIcon />}
                          label="Google"
                          color="secondary"
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Tooltip title={user.is_admin ? 'Remover Admin' : 'Tornar Admin'}>
                      <IconButton
                        size="small"
                        onClick={() => handleToggleAdminStatus(user.id)}
                        disabled={user.email === '<EMAIL>'}
                        color={user.is_admin ? 'primary' : 'default'}
                      >
                        <SecurityIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{formatDate(user.created_at)}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title={user.is_active ? 'Desativar' : 'Ativar'}>
                        <IconButton
                          size="small"
                          onClick={() => handleToggleUserStatus(user.id)}
                          disabled={user.email === '<EMAIL>'}
                        >
                          <Switch
                            checked={user.is_active}
                            size="small"
                            disabled={user.email === '<EMAIL>'}
                          />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Deletar">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteUser(user.id)}
                          disabled={user.email === '<EMAIL>'}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Create User Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Criar Novo Usuário</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Senha"
              type="password"
              value={newUser.password}
              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Nome"
              value={newUser.first_name}
              onChange={(e) => setNewUser({ ...newUser, first_name: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Sobrenome"
              value={newUser.last_name}
              onChange={(e) => setNewUser({ ...newUser, last_name: e.target.value })}
              margin="normal"
              required
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancelar</Button>
          <Button onClick={handleCreateUser} variant="contained">
            Criar Usuário
          </Button>
        </DialogActions>
      </Dialog>
        </>
      )}

      {/* Tab 2: Configurações */}
      {currentTab === 1 && (
        <Box>
          <Typography variant="h5" gutterBottom>
            Configurações do Sistema
          </Typography>

          <Grid container spacing={3}>
            {/* Google OAuth Configuration */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <GoogleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Google OAuth
                  </Typography>

                  {googleOAuthStatus && (
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <Chip
                            size="small"
                            label={googleOAuthStatus.google_oauth_enabled ? 'Habilitado' : 'Desabilitado'}
                            color={googleOAuthStatus.google_oauth_enabled ? 'success' : 'error'}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Status do OAuth"
                          secondary={googleOAuthStatus.google_oauth_enabled ? 'Configurado e funcionando' : 'Não configurado'}
                        />
                      </ListItem>

                      <ListItem>
                        <ListItemIcon>
                          <Chip
                            size="small"
                            label={googleOAuthStatus.client_id_configured ? 'OK' : 'Faltando'}
                            color={googleOAuthStatus.client_id_configured ? 'success' : 'error'}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Client ID"
                          secondary="GOOGLE_CLIENT_ID"
                        />
                      </ListItem>

                      <ListItem>
                        <ListItemIcon>
                          <Chip
                            size="small"
                            label={googleOAuthStatus.client_secret_configured ? 'OK' : 'Faltando'}
                            color={googleOAuthStatus.client_secret_configured ? 'success' : 'error'}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary="Client Secret"
                          secondary="GOOGLE_CLIENT_SECRET"
                        />
                      </ListItem>

                      <ListItem>
                        <ListItemIcon>
                          <LinkIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Redirect URI"
                          secondary={googleOAuthStatus.redirect_uri}
                        />
                      </ListItem>
                    </List>
                  )}

                  {googleOAuthStatus?.google_oauth_enabled && (
                    <Button
                      variant="outlined"
                      startIcon={<GoogleIcon />}
                      onClick={handleGoogleLogin}
                      fullWidth
                      sx={{ mt: 2 }}
                    >
                      Testar Login Google
                    </Button>
                  )}

                  {!googleOAuthStatus?.google_oauth_enabled && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      Para habilitar o Google OAuth, configure as variáveis de ambiente:
                      <br />• GOOGLE_CLIENT_ID
                      <br />• GOOGLE_CLIENT_SECRET
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* System Information */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Informações do Sistema
                  </Typography>

                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <PersonIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Total de Usuários"
                        secondary={`${users.length} usuários cadastrados`}
                      />
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <AdminIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Administradores"
                        secondary={`${users.filter(u => u.is_admin || u.email === '<EMAIL>').length} administradores`}
                      />
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <GoogleIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Contas Google"
                        secondary={`${users.filter(u => u.google_id).length} contas vinculadas`}
                      />
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <VerifiedIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Usuários Verificados"
                        secondary={`${users.filter(u => u.is_verified).length} usuários verificados`}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default UserManagement;
