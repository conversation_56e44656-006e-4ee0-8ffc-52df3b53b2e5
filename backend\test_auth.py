#!/usr/bin/env python3
"""
Script para testar autenticação
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.security import create_access_token, get_current_user, oauth2_scheme
from app.database import SessionLocal
from app.models.financial import User
from datetime import timedelta
import asyncio
import logging

# Configurar logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_auth():
    """Testa o sistema de autenticação"""
    
    db = SessionLocal()
    
    try:
        # Buscar usuário admin
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not user:
            logger.error("Usuário <EMAIL> não encontrado")
            return False
        
        logger.info(f"Usuário encontrado: {user.email} (ID: {user.id}, Ativo: {user.is_active})")
        
        # Criar token
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        
        logger.info(f"Token criado: {access_token[:50]}...")
        
        # Testar validação do token
        try:
            current_user = await get_current_user(token=access_token, db=db)
            logger.info(f"Token validado com sucesso! Usuário: {current_user.email}")
            return True
        except Exception as e:
            logger.error(f"Erro ao validar token: {str(e)}")
            return False
        
    except Exception as e:
        logger.error(f"Erro no teste: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = asyncio.run(test_auth())
    if success:
        print("✅ Autenticação funcionando!")
    else:
        print("❌ Problema na autenticação")
        sys.exit(1)
