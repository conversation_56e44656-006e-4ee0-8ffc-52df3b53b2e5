from pydantic_settings import BaseSettings
from functools import lru_cache
import os

# Obter o caminho absoluto para o arquivo do banco de dados na raiz do projeto
BACKEND_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = os.path.dirname(BACKEND_DIR)
DB_PATH = os.path.join(PROJECT_ROOT, "finance_app.db")
DB_URL = f"sqlite:///{DB_PATH}"

class Settings(BaseSettings):
    DATABASE_URL: str = DB_URL
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Finance App API"

    model_config = {
        "env_file": ".env",
        "extra": "allow"  # Permitir campos extras do .env
    }

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()