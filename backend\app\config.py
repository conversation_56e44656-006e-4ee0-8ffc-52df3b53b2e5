import os
from pydantic_settings import BaseSettings
from functools import lru_cache

# Obter o caminho absoluto para o arquivo do banco de dados na raiz do projeto
BACKEND_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = os.path.dirname(BACKEND_DIR)
DB_PATH = os.path.join(PROJECT_ROOT, "finance_app.db")
DB_URL = f"sqlite:///{DB_PATH}"

class Settings(BaseSettings):
    DATABASE_URL: str = DB_URL
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Finance App API"

    # Configurações de segurança (para compatibilidade com env vars existentes)
    SECRET_KEY: str = "3ccb93593cb1a3aa4b7a842166cb47c230d813ed6372f1c1fe255e2443654cf5"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Configurações de IA (opcionais)
    AI_ENABLED: bool = False
    AI_PROVIDER: str = "ollama"
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama3:latest"
    AI_REQUEST_TIMEOUT: int = 60
    AI_MAX_TOKENS: int = 2000
    AI_TEMPERATURE: float = 0.7

    # Configurações do Google OAuth2 (opcionais)
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = "http://localhost:8000/api/v1/auth/google/callback"

    model_config = {"env_file": ".env"}

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()