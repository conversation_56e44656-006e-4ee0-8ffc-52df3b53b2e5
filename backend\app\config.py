import os
import json
from pydantic_settings import BaseSettings
from functools import lru_cache
from typing import Optional

# Obter o caminho absoluto para o arquivo do banco de dados na raiz do projeto
BACKEND_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = os.path.dirname(BACKEND_DIR)
DB_PATH = os.path.join(PROJECT_ROOT, "finance_app.db")
DB_URL = f"sqlite:///{DB_PATH}"

def load_oauth_credentials(external_path: Optional[str] = None) -> dict:
    """
    Carrega credenciais OAuth de um arquivo externo

    Args:
        external_path: Caminho para o arquivo oauth.txt (opcional)

    Returns:
        Dicionário com as credenciais OAuth
    """
    oauth_data = {
        "GOOGLE_CLIENT_ID": "",
        "GOOGLE_CLIENT_SECRET": ""
    }

    # Lista de possíveis locais para o arquivo oauth.txt
    possible_paths = []

    if external_path:
        possible_paths.append(external_path)

    # Adicionar caminhos padrão
    possible_paths.extend([
        os.path.join(PROJECT_ROOT, "oauth.txt"),
        os.path.join(os.path.expanduser("~"), "oauth.txt"),
        os.path.join(os.path.expanduser("~"), "Documents", "oauth.txt"),
        os.path.join("C:", "oauth", "oauth.txt") if os.name == 'nt' else "/etc/oauth/oauth.txt"
    ])

    for path in possible_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                    # Tentar como JSON primeiro
                    try:
                        data = json.loads(content)
                        if isinstance(data, dict):
                            oauth_data.update(data)
                            print(f"✅ Credenciais OAuth carregadas de: {path}")
                            return oauth_data
                    except json.JSONDecodeError:
                        pass

                    # Tentar como formato chave=valor
                    for line in content.split('\n'):
                        line = line.strip()
                        if '=' in line and not line.startswith('#'):
                            key, value = line.split('=', 1)
                            key = key.strip().upper()
                            value = value.strip().strip('"').strip("'")
                            if key in oauth_data:
                                oauth_data[key] = value

                    print(f"✅ Credenciais OAuth carregadas de: {path}")
                    return oauth_data

            except Exception as e:
                print(f"⚠️ Erro ao ler arquivo OAuth {path}: {e}")
                continue

    print("⚠️ Nenhum arquivo oauth.txt encontrado. OAuth será desabilitado.")
    return oauth_data

class Settings(BaseSettings):
    DATABASE_URL: str = DB_URL
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Finance App API"

    # Configurações de segurança
    SECRET_KEY: str = "3ccb93593cb1a3aa4b7a842166cb47c230d813ed6372f1c1fe255e2443654cf5"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    MAX_LOGIN_ATTEMPTS: int = 5
    LOGIN_BLOCK_MINUTES: int = 30

    # Configurações de IA
    AI_ENABLED: bool = False
    AI_PROVIDER: str = "ollama"
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama3:latest"
    AI_REQUEST_TIMEOUT: int = 60
    AI_MAX_TOKENS: int = 2000
    AI_TEMPERATURE: float = 0.7
    AI_MAX_REQUESTS_PER_DAY: int = 10
    AI_MAX_TOKENS_PER_DAY: int = 10000
    AI_REQUIRE_USER_CONSENT: bool = True

    # Configurações do Google OAuth2 (carregadas de arquivo externo)
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = "http://127.0.0.1:8000/api/v1/auth/google/callback"

    # Caminho externo para arquivo OAuth (opcional)
    OAUTH_FILE_PATH: Optional[str] = None

    model_config = {"env_file": ".env"}

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Carregar credenciais OAuth de arquivo externo
        oauth_creds = load_oauth_credentials(self.OAUTH_FILE_PATH)
        self.GOOGLE_CLIENT_ID = oauth_creds.get("GOOGLE_CLIENT_ID", "")
        self.GOOGLE_CLIENT_SECRET = oauth_creds.get("GOOGLE_CLIENT_SECRET", "")

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()