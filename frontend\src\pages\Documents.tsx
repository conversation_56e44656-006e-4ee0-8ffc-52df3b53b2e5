import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Snackbar,
  Alert,
  Paper
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useFilters } from '../contexts/FiltersContext';
import GlobalFilters from '../components/GlobalFilters/GlobalFilters';
import CollapsibleNavBar from '../components/Navigation/CollapsibleNavBar';
import DocumentProcessor from '../components/Analytics/DocumentProcessor';
import TestUploadAuth from '../components/Analytics/TestUploadAuth';
import api from '../services/api';

interface Transaction {
  id: number;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: string;
  subtype: string;
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  is_investment?: boolean;
  is_refund?: boolean;
  owner_id?: number | null;
  owner?: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
  };
}

const Documents: React.FC = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [feedback, setFeedback] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const { filters } = useFilters();

  useEffect(() => {
    let mounted = true;

    const checkAdminStatus = async () => {
      try {
        const response = await api.get('/auth/users/me');
        if (mounted && response.data) {
          setIsAdmin(response.data.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de admin:', error);
        if (mounted) {
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();

    // Listen for refreshTransactions event from DocumentProcessor
    const handleRefreshTransactions = () => {
      console.log('Recebido evento refreshTransactions - documentos processados');
      setFeedback({
        open: true,
        message: 'Documentos processados com sucesso!',
        severity: 'success'
      });
    };

    window.addEventListener('refreshTransactions', handleRefreshTransactions);

    return () => {
      mounted = false;
      window.removeEventListener('refreshTransactions', handleRefreshTransactions);
    };
  }, []);

  const handleCloseFeedback = () => {
    setFeedback(prev => ({
      ...prev,
      open: false
    }));
  };

  const handleTransactionsExtracted = async (extractedTransactions: Transaction[]) => {
    console.log('handleTransactionsExtracted chamado:', extractedTransactions.length);
    // Não fazemos nada aqui para evitar duplicação de transações
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Barra de Navegação Colapsável */}
      <CollapsibleNavBar isAdmin={isAdmin} title="Finance App - Documentos" />

      {/* Container centralizado com 80% de largura para todo o conteúdo */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Typography variant="h4" gutterBottom>
          Processamento de Documentos
        </Typography>

        {/* Filtros Globais */}
        <GlobalFilters
          filters={filters}
          onFiltersChange={() => {}} // Será gerenciado pelo contexto
          showFamilyFilter={true}
        />

        {/* Ferramenta de diagnóstico de autenticação */}
        <TestUploadAuth />

        {/* Processador de Documentos */}
        <Paper sx={{ p: 4 }}>
          <DocumentProcessor onTransactionsExtracted={handleTransactionsExtracted} />
        </Paper>

        <Snackbar
          open={feedback.open}
          autoHideDuration={6000}
          onClose={handleCloseFeedback}
        >
          <Alert
            onClose={handleCloseFeedback}
            severity={feedback.severity}
            variant="filled"
          >
            {feedback.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Documents;
