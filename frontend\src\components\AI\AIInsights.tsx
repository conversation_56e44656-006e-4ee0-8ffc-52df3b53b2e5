import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Lightbulb as LightbulbIcon } from '@mui/icons-material';
import aiService, { Insight } from '../../services/aiService';

interface AIInsightsProps {
  onFeedback?: (message: string, severity: 'success' | 'info' | 'warning' | 'error') => void;
  filters?: {
    startDate: Date | null;
    endDate: Date | null;
    type: string;
    category: string;
  };
}

const AIInsights: React.FC<AIInsightsProps> = ({ onFeedback, filters }) => {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [periodDialogOpen, setPeriodDialogOpen] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('year');

  // Determinar o período com base nos filtros
  const determinePeriod = () => {
    if (!filters || (!filters.startDate && !filters.endDate)) {
      // Se não houver filtros definidos, abrir diálogo para escolher período
      setPeriodDialogOpen(true);
      return null;
    }

    // Se houver filtros definidos, usar o período dos filtros
    if (filters.startDate && filters.endDate) {
      const start = new Date(filters.startDate);
      const end = new Date(filters.endDate);
      const diffDays = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      if (diffDays <= 7) return 'week';
      if (diffDays <= 31) return 'month';
      if (diffDays <= 92) return 'quarter';
      return 'year';
    }

    // Se apenas uma das datas estiver definida, usar o ano atual
    return 'year';
  };

  const generateInsights = async (period?: string) => {
    setLoading(true);
    setError(null);

    try {
      // Verificar se o período foi passado como parâmetro ou deve ser determinado
      const timePeriod = period || determinePeriod();

      // Se o período for nulo, significa que o diálogo de período será aberto
      if (timePeriod === null) {
        setLoading(false);
        return;
      }

      const response = await aiService.getInsights(timePeriod);
      setInsights(response.insights);

      if (onFeedback && response.insights.length > 0) {
        onFeedback('Insights financeiros gerados com sucesso!', 'success');
      } else if (onFeedback && response.insights.length === 0) {
        onFeedback('Não há insights disponíveis no momento.', 'info');
      }
    } catch (err) {
      console.error('Erro ao gerar insights:', err);
      setError('Não foi possível gerar insights. Por favor, tente novamente.');
      if (onFeedback) {
        onFeedback('Erro ao gerar insights financeiros.', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  // Função para lidar com a seleção de período no diálogo
  const handlePeriodSelect = () => {
    setPeriodDialogOpen(false);
    generateInsights(selectedPeriod);
  };

  // Função para cancelar o diálogo
  const handlePeriodCancel = () => {
    setPeriodDialogOpen(false);
    setLoading(false);
  };

  return (
    <Paper sx={{ p: 3, mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h2" sx={{ display: 'flex', alignItems: 'center' }}>
          Insights Financeiros
          <Box
            component="span"
            sx={{
              ml: 1,
              fontSize: '0.7rem',
              bgcolor: '#E1F5FE',
              color: '#0288D1',
              p: '0.2rem 0.4rem',
              borderRadius: '4px',
              fontWeight: 'normal'
            }}
          >
            IA
          </Box>
        </Typography>
        <Button
          variant="contained"
          startIcon={<LightbulbIcon />}
          onClick={() => generateInsights()}
          disabled={loading}
          size="small"
        >
          Gerar Insights
        </Button>
      </Box>

      {/* Diálogo de seleção de período */}
      <Dialog open={periodDialogOpen} onClose={handlePeriodCancel}>
        <DialogTitle>Selecione o Período para Análise</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Escolha o período de tempo para o qual deseja gerar insights financeiros.
          </Typography>
          <FormControl fullWidth sx={{ mt: 1 }}>
            <InputLabel id="period-select-label">Período</InputLabel>
            <Select
              labelId="period-select-label"
              value={selectedPeriod}
              label="Período"
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <MenuItem value="week">Semana atual</MenuItem>
              <MenuItem value="month">Mês atual</MenuItem>
              <MenuItem value="quarter">Trimestre atual</MenuItem>
              <MenuItem value="year">Ano atual</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePeriodCancel}>Cancelar</Button>
          <Button onClick={handlePeriodSelect} variant="contained">
            Gerar Insights
          </Button>
        </DialogActions>
      </Dialog>

      {loading && (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Analisando seus dados financeiros...
          </Typography>
        </Box>
      )}

      {error && !loading && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && insights.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body2" color="text.secondary">
            Clique em "Gerar Insights" para obter análises personalizadas baseadas em IA.
          </Typography>
        </Box>
      )}

      {!loading && !error && insights.length > 0 && (
        <Grid container spacing={2}>
          {insights.map((insight, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  borderLeft: '4px solid #2196F3',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
                  }
                }}
              >
                <CardContent>
                  <Typography variant="h6" component="h3" color="#2196F3" gutterBottom sx={{ fontSize: '1.1rem', fontWeight: 600 }}>
                    {insight.title}
                  </Typography>
                  <Typography variant="body2">
                    {insight.explanation}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Paper>
  );
};

export default AIInsights;
