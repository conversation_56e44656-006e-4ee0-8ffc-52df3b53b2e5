import React, { useState, useEffect } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  Button,
  Box,
  Collapse,
  IconButton,
  Fab,
  useScrollTrigger,
  Slide,
  Paper
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as PlanningIcon,
  Receipt as ReceiptIcon,
  FamilyRestroom as FamilyIcon,
  People as PeopleIcon,
  ExitToApp as LogoutIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface CollapsibleNavBarProps {
  isAdmin?: boolean;
  title?: string;
}

interface HideOnScrollProps {
  children: React.ReactElement;
}

function HideOnScroll(props: HideOnScrollProps) {
  const { children } = props;
  const trigger = useScrollTrigger({
    target: undefined,
  });

  return (
    <Slide appear={false} direction="down" in={!trigger}>
      {children}
    </Slide>
  );
}

const CollapsibleNavBar: React.FC<CollapsibleNavBarProps> = ({ 
  isAdmin = false, 
  title = "Finance App" 
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showFloatingIcon, setShowFloatingIcon] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { signOut } = useAuth();

  // Detectar scroll para mostrar/esconder o ícone flutuante
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setShowFloatingIcon(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    signOut();
    navigate('/login');
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const getActiveRoute = () => {
    const path = location.pathname;
    if (path === '/dashboard' || path === '/') return 'dashboard';
    if (path === '/analysis') return 'analysis';
    if (path === '/planning') return 'planning';
    if (path === '/documents') return 'documents';
    if (path === '/family') return 'family';
    if (path === '/users') return 'users';
    return '';
  };

  const activeRoute = getActiveRoute();

  const navigationItems = [
    {
      key: 'dashboard',
      label: 'VISÃO GERAL',
      icon: <DashboardIcon />,
      path: '/dashboard'
    },
    {
      key: 'analysis',
      label: 'ANÁLISE',
      icon: <TrendingUpIcon />,
      path: '/analysis'
    },
    {
      key: 'planning',
      label: 'PLANEJAMENTO',
      icon: <PlanningIcon />,
      path: '/planning'
    },
    {
      key: 'documents',
      label: 'DOCUMENTOS',
      icon: <ReceiptIcon />,
      path: '/documents'
    },
    {
      key: 'family',
      label: 'FAMÍLIA',
      icon: <FamilyIcon />,
      path: '/family'
    }
  ];

  if (isAdmin) {
    navigationItems.push({
      key: 'users',
      label: 'USUÁRIOS',
      icon: <PeopleIcon />,
      path: '/users'
    });
  }

  return (
    <>
      {/* AppBar Principal */}
      <HideOnScroll>
        <AppBar position="fixed" sx={{ zIndex: 1201 }}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              {title}
            </Typography>
            
            {/* Botão de colapsar/expandir */}
            <IconButton
              color="inherit"
              onClick={handleToggleCollapse}
              sx={{ mr: 1 }}
            >
              {isCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </IconButton>

            {/* Botão de logout */}
            <Button 
              color="inherit" 
              onClick={handleLogout} 
              startIcon={<LogoutIcon />}
            >
              Sair
            </Button>
          </Toolbar>

          {/* Menu de Navegação Colapsável */}
          <Collapse in={!isCollapsed}>
            <Paper 
              elevation={0} 
              sx={{ 
                backgroundColor: '#f5f5f5', 
                borderTop: '1px solid #ddd',
                py: 1
              }}
            >
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                flexWrap: 'wrap',
                gap: 1,
                px: 2
              }}>
                {navigationItems.map((item) => (
                  <Button
                    key={item.key}
                    variant={activeRoute === item.key ? "contained" : "text"}
                    startIcon={item.icon}
                    onClick={() => navigate(item.path)}
                    sx={{ 
                      color: activeRoute === item.key ? 'white' : '#1976d2',
                      backgroundColor: activeRoute === item.key ? '#1976d2' : 'transparent',
                      '&:hover': {
                        backgroundColor: activeRoute === item.key ? '#1565c0' : 'rgba(25, 118, 210, 0.04)'
                      },
                      minWidth: 'auto',
                      px: 2
                    }}
                  >
                    {item.label}
                  </Button>
                ))}
              </Box>
            </Paper>
          </Collapse>
        </AppBar>
      </HideOnScroll>

      {/* Ícone Flutuante */}
      {showFloatingIcon && (
        <Fab
          color="primary"
          aria-label="menu"
          onClick={handleToggleCollapse}
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: 1300,
            transition: 'all 0.3s ease-in-out',
            transform: showFloatingIcon ? 'scale(1)' : 'scale(0)',
          }}
        >
          <MenuIcon />
        </Fab>
      )}

      {/* Spacer para compensar a AppBar fixa */}
      <Box sx={{ 
        height: isCollapsed ? '64px' : '120px', 
        transition: 'height 0.3s ease-in-out' 
      }} />
    </>
  );
};

export default CollapsibleNavBar;
