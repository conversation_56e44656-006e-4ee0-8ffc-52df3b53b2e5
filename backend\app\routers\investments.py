from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional
from datetime import datetime, date

from ..database import get_db
from ..models.financial import User, Investment, Transaction
from ..schemas.investment import (
    InvestmentCreate,
    InvestmentResponse,
    InvestmentUpdate,
    InvestmentSummary
)
from ..security import get_current_active_user
import logging

router = APIRouter(
    prefix="/investments",
    tags=["investments"],
)

logger = logging.getLogger(__name__)


@router.get("/", response_model=List[InvestmentResponse])
async def get_investments(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Retorna todos os investimentos do usuário.
    """
    try:
        investments = db.query(Investment).filter(
            Investment.user_id == current_user.id
        ).order_by(Investment.created_at.desc()).offset(skip).limit(limit).all()
        return investments
    except Exception as e:
        logger.error(f"Erro ao buscar investimentos: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao buscar investimentos"
        )


@router.post("/", response_model=InvestmentResponse)
async def create_investment(
    investment: InvestmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Cria um novo investimento.
    """
    try:
        db_investment = Investment(
            **investment.dict(),
            user_id=current_user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(db_investment)
        db.commit()
        db.refresh(db_investment)

        # Registrar uma transação para este investimento
        transaction = Transaction(
            user_id=current_user.id,
            description=f"Investimento: {db_investment.name}",
            amount=db_investment.initial_amount,
            type="investment",
            category="investment",
            date=db_investment.start_date,
            subtype="deposit",
            investment_id=db_investment.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(transaction)
        db.commit()

        return db_investment
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao criar investimento: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao criar investimento"
        )


@router.get("/{investment_id}", response_model=InvestmentResponse)
async def get_investment(
    investment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Retorna um investimento específico.
    """
    investment = db.query(Investment).filter(
        Investment.id == investment_id,
        Investment.user_id == current_user.id
    ).first()

    if not investment:
        raise HTTPException(
            status_code=404,
            detail="Investimento não encontrado"
        )

    return investment


@router.put("/{investment_id}", response_model=InvestmentResponse)
async def update_investment(
    investment_id: int,
    investment_update: InvestmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Atualiza um investimento.
    """
    db_investment = db.query(Investment).filter(
        Investment.id == investment_id,
        Investment.user_id == current_user.id
    ).first()

    if not db_investment:
        raise HTTPException(
            status_code=404,
            detail="Investimento não encontrado"
        )

    try:
        for key, value in investment_update.dict(exclude_unset=True).items():
            setattr(db_investment, key, value)

        db_investment.updated_at = datetime.now()
        db.commit()
        db.refresh(db_investment)
        return db_investment
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao atualizar investimento: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao atualizar investimento"
        )


@router.delete("/{investment_id}")
async def delete_investment(
    investment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Exclui um investimento.
    """
    db_investment = db.query(Investment).filter(
        Investment.id == investment_id,
        Investment.user_id == current_user.id
    ).first()

    if not db_investment:
        raise HTTPException(
            status_code=404,
            detail="Investimento não encontrado"
        )

    try:
        # Excluir transações relacionadas primeiro
        db.query(Transaction).filter(
            Transaction.investment_id == investment_id
        ).delete()

        # Excluir o investimento
        db.delete(db_investment)
        db.commit()
        return {"message": "Investimento excluído com sucesso"}
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao excluir investimento: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao excluir investimento"
        )


@router.post("/{investment_id}/update-value")
async def update_investment_value(
    investment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Atualiza o valor atual do investimento (simulado).
    Em um sistema real, isso poderia integrar com APIs externas para obter o valor atual.
    """
    db_investment = db.query(Investment).filter(
        Investment.id == investment_id,
        Investment.user_id == current_user.id
    ).first()

    if not db_investment:
        raise HTTPException(
            status_code=404,
            detail="Investimento não encontrado"
        )

    try:
        # Simular variação do valor com base no tipo e tempo de investimento
        from datetime import datetime
        import random

        start_date = db_investment.start_date
        days_invested = (datetime.now().date() - start_date).days

        # Definir volatilidade com base no tipo de investimento
        volatility = {
            "savings": 0.001,  # Poupança - baixa volatilidade
            "bonds": 0.002,    # Renda fixa - baixa volatilidade
            "funds": 0.005,    # Fundos - média volatilidade
            "stocks": 0.01,    # Ações - alta volatilidade
            "crypto": 0.03,    # Cripto - muito alta volatilidade
            "real_estate": 0.003,  # Imóveis - média volatilidade
            "other": 0.002     # Outros - média volatilidade
        }.get(db_investment.type, 0.002)

        # Calcular variação percentual simulada
        variation = random.normalvariate(0.0005, volatility) * days_invested

        # Limite a variação máxima em bases diárias para ser realista
        if variation > 0.15:  # Limitar em 15%
            variation = 0.15
        elif variation < -0.10:  # Limitar queda em 10%
            variation = -0.10

        # Adicionar variação ao valor atual
        old_value = db_investment.current_amount
        new_value = db_investment.initial_amount * (1 + variation)

        # Garantir que o valor não fique negativo
        if new_value < 0:
            new_value = db_investment.current_amount * 0.9  # Queda máxima de 10%

        db_investment.current_amount = new_value
        db_investment.updated_at = datetime.now()
        db.commit()
        db.refresh(db_investment)

        return {
            "message": "Valor do investimento atualizado com sucesso",
            "previous_value": old_value,
            "current_value": db_investment.current_amount,
            "variation": db_investment.current_amount - old_value,
            "variation_percent": ((db_investment.current_amount / old_value) - 1) * 100 if old_value > 0 else 0
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao atualizar valor do investimento: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao atualizar valor do investimento"
        )


@router.get("/summary", response_model=InvestmentSummary)
async def get_investment_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Retorna um resumo dos investimentos do usuário.
    """
    try:
        # Obter todos os investimentos do usuário
        investments = db.query(Investment).filter(
            Investment.user_id == current_user.id
        ).all()

        if not investments:
            return {
                "total_deposits": 0,
                "total_withdrawals": 0,
                "net_investment": 0,
                "total_profit_loss": 0,
                "profit_percentage": 0
            }

        # Calcular valores totais dos investimentos
        total_initial = sum(inv.initial_amount for inv in investments)
        total_current = sum(inv.current_amount for inv in investments)

        # Obter depósitos e retiradas relacionados a investimentos
        deposits = db.query(func.sum(Transaction.amount)).filter(
            Transaction.user_id == current_user.id,
            Transaction.type == "investment",
            Transaction.subtype == "deposit"
        ).scalar() or 0

        withdrawals = db.query(func.sum(Transaction.amount)).filter(
            Transaction.user_id == current_user.id,
            Transaction.type == "investment",
            Transaction.subtype == "withdrawal"
        ).scalar() or 0

        # Calcular valores
        net_investment = deposits - withdrawals
        profit_loss = total_current - net_investment
        profit_percentage = (profit_loss / net_investment) * 100 if net_investment > 0 else 0

        return {
            "total_deposits": deposits,
            "total_withdrawals": withdrawals,
            "net_investment": net_investment,
            "total_profit_loss": profit_loss,
            "profit_percentage": profit_percentage
        }
    except Exception as e:
        logger.error(f"Erro ao gerar resumo de investimentos: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao gerar resumo de investimentos"
        )