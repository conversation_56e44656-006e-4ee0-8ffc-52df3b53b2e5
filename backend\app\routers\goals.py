from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..models.financial import User, FinancialGoal, Contribution
from ..schemas.financial import (
    FinancialGoalCreate,
    FinancialGoal as FinancialGoalSchema,
    FinancialGoalUpdate,
    ContributionCreate,
    Contribution as ContributionSchema
)
from ..security import get_current_user
import logging
from datetime import datetime, timedelta
from sqlalchemy import func, desc

# Configurar logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/goals",
    tags=["financial goals"],
    responses={404: {"description": "Not found"}}
)

@router.post("/", response_model=FinancialGoalSchema)
def create_goal(
    goal: FinancialGoalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cria uma nova meta financeira.
    """

    db_goal = FinancialGoal(
        user_id=current_user.id,
        name=goal.name,
        target_amount=goal.target_amount,
        deadline=goal.deadline,
        description=goal.description,
        category=goal.category,
        priority=goal.priority,
        created_at=datetime.now()
    )

    db.add(db_goal)
    db.commit()
    db.refresh(db_goal)

    logger.info(f"Meta financeira criada com sucesso: ID {db_goal.id}")
    return db_goal

@router.get("/", response_model=List[FinancialGoalSchema])
def read_goals(
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    completed: Optional[bool] = Query(None, description="Filtrar por metas completadas"),
    category: Optional[str] = Query(None, description="Filtrar por categoria"),
    db: Session = Depends(get_db)
):
    """
    Retorna todas as metas financeiras do usuário atual.
    """
    query = db.query(FinancialGoal).filter(FinancialGoal.user_id == current_user.id)

    if completed is not None:
        query = query.filter(FinancialGoal.is_completed == completed)

    if category:
        query = query.filter(FinancialGoal.category == category)

    goals = query.order_by(FinancialGoal.deadline, FinancialGoal.priority.desc()).offset(skip).limit(limit).all()
    return goals

@router.get("/{goal_id}", response_model=FinancialGoalSchema)
def read_goal(
    goal_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retorna uma meta financeira específica pelo ID.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    return db_goal

@router.put("/{goal_id}", response_model=FinancialGoalSchema)
def update_goal(
    goal_id: int,
    goal_update: FinancialGoalUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Atualiza uma meta financeira existente.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    # Atualizar apenas os campos não nulos
    update_data = goal_update.model_dump(exclude_unset=True, exclude_none=True)

    for field, value in update_data.items():
        setattr(db_goal, field, value)

    # Se a meta está completa, atualizar a data de conclusão
    if update_data.get("is_completed") and not db_goal.completed_at:
        db_goal.completed_at = datetime.now()
    # Se a meta foi desmarcada como completa, limpar data de conclusão
    elif "is_completed" in update_data and not update_data["is_completed"]:
        db_goal.completed_at = None

    db.commit()
    db.refresh(db_goal)
    return db_goal

@router.delete("/{goal_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_goal(
    goal_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove uma meta financeira existente.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    # Remover todas as contribuições relacionadas
    db.query(Contribution).filter(Contribution.goal_id == goal_id).delete()

    # Remover a meta
    db.delete(db_goal)
    db.commit()
    return None

@router.post("/{goal_id}/contributions/", response_model=ContributionSchema)
def create_contribution(
    goal_id: int,
    contribution: ContributionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Adiciona uma nova contribuição a uma meta financeira.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    db_contribution = Contribution(
        goal_id=goal_id,
        amount=contribution.amount,
        date=contribution.date or datetime.now(),
        description=contribution.description
    )

    db.add(db_contribution)

    # Verificar se a meta foi atingida
    total_contributed = db.query(func.sum(Contribution.amount)).filter(
        Contribution.goal_id == goal_id
    ).scalar() or 0

    total_contributed += contribution.amount

    if total_contributed >= db_goal.target_amount and not db_goal.is_completed:
        db_goal.is_completed = True
        db_goal.completed_at = datetime.now()
        logger.info(f"Meta financeira {goal_id} atingida")

    db.commit()
    db.refresh(db_contribution)
    return db_contribution

@router.get("/{goal_id}/contributions/", response_model=List[ContributionSchema])
def read_contributions(
    goal_id: int,
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Retorna todas as contribuições para uma meta financeira específica.
    """
    # Verificar se a meta existe e pertence ao usuário
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    contributions = db.query(Contribution).filter(
        Contribution.goal_id == goal_id
    ).order_by(desc(Contribution.date)).offset(skip).limit(limit).all()

    return contributions

@router.delete("/{goal_id}/contributions/{contribution_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_contribution(
    goal_id: int,
    contribution_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove uma contribuição de uma meta financeira.
    """
    # Verificar se a meta existe e pertence ao usuário
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    # Buscar a contribuição
    db_contribution = db.query(Contribution).filter(
        Contribution.id == contribution_id,
        Contribution.goal_id == goal_id
    ).first()

    if not db_contribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contribuição não encontrada"
        )

    # Remover a contribuição
    db.delete(db_contribution)

    # Recalcular se a meta ainda está completa
    total_contributed = db.query(func.sum(Contribution.amount)).filter(
        Contribution.goal_id == goal_id
    ).scalar() or 0

    # Atualizar o status da meta
    if db_goal.is_completed and total_contributed < db_goal.target_amount:
        db_goal.is_completed = False
        db_goal.completed_at = None

    db.commit()
    return None

@router.get("/summary/progress")
def get_goals_progress(
    current_user: User = Depends(get_current_user),
    include_completed: bool = Query(False, description="Incluir metas já completadas"),
    db: Session = Depends(get_db)
):
    """
    Retorna um resumo do progresso de todas as metas financeiras.
    """
    query = db.query(FinancialGoal).filter(FinancialGoal.user_id == current_user.id)

    if not include_completed:
        query = query.filter(FinancialGoal.is_completed == False)

    goals = query.order_by(FinancialGoal.deadline).all()

    goals_progress = []

    for goal in goals:
        # Buscar o total de contribuições para esta meta
        contributed = db.query(func.sum(Contribution.amount)).filter(
            Contribution.goal_id == goal.id
        ).scalar() or 0

        # Calcular percentual concluído
        percentage = min(100, (contributed / goal.target_amount) * 100) if goal.target_amount > 0 else 0

        # Calcular quanto falta para atingir a meta
        remaining = max(0, goal.target_amount - contributed)

        # Se houver deadline, calcular dias restantes e média diária necessária
        days_remaining = None
        daily_amount_needed = None

        if goal.deadline and not goal.is_completed:
            days_remaining = (goal.deadline - datetime.now()).days
            if days_remaining > 0 and remaining > 0:
                daily_amount_needed = remaining / days_remaining

        goals_progress.append({
            "id": goal.id,
            "name": goal.name,
            "target_amount": goal.target_amount,
            "contributed": contributed,
            "remaining": remaining,
            "percentage": percentage,
            "days_remaining": days_remaining,
            "daily_amount_needed": daily_amount_needed,
            "deadline": goal.deadline,
            "category": goal.category,
            "priority": goal.priority,
            "is_completed": goal.is_completed,
            "completed_at": goal.completed_at
        })

    return {"goals_progress": goals_progress}

@router.get("/{goal_id}/forecast")
def get_goal_forecast(
    goal_id: int,
    projection_months: int = Query(6, description="Meses para projeção", ge=1, le=60),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Fornece uma previsão para atingir uma meta financeira específica.
    """
    # Verificar se a meta existe e pertence ao usuário
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()

    if not db_goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta financeira não encontrada"
        )

    if db_goal.is_completed:
        return {
            "goal_id": goal_id,
            "status": "completed",
            "completion_date": db_goal.completed_at,
            "message": "Meta já foi atingida"
        }

    # Buscar o total de contribuições para esta meta
    contributed = db.query(func.sum(Contribution.amount)).filter(
        Contribution.goal_id == goal_id
    ).scalar() or 0

    # Calcular quanto falta para atingir a meta
    remaining = max(0, db_goal.target_amount - contributed)

    # Buscar contribuições dos últimos 3 meses para calcular média mensal
    three_months_ago = datetime.now() - timedelta(days=90)
    recent_contributions = db.query(func.sum(Contribution.amount)).filter(
        Contribution.goal_id == goal_id,
        Contribution.date >= three_months_ago
    ).scalar() or 0

    # Calcular média mensal (usando 3 meses)
    monthly_avg = recent_contributions / 3

    forecast = {}
    if monthly_avg > 0:
        # Meses estimados para atingir a meta com média atual
        months_needed = remaining / monthly_avg
        estimated_completion = datetime.now() + timedelta(days=30*months_needed)

        forecast = {
            "goal_id": goal_id,
            "target_amount": db_goal.target_amount,
            "contributed": contributed,
            "remaining": remaining,
            "monthly_avg_contribution": monthly_avg,
            "months_needed": round(months_needed, 1),
            "estimated_completion_date": estimated_completion,
            "will_meet_deadline": db_goal.deadline > estimated_completion if db_goal.deadline else None
        }
    else:
        forecast = {
            "goal_id": goal_id,
            "target_amount": db_goal.target_amount,
            "contributed": contributed,
            "remaining": remaining,
            "monthly_avg_contribution": 0,
            "message": "Sem contribuições recentes para calcular previsão"
        }

    # Se há deadline definido, calcular contribuição necessária para atingir
    if db_goal.deadline:
        days_to_deadline = (db_goal.deadline - datetime.now()).days
        if days_to_deadline > 0:
            months_to_deadline = days_to_deadline / 30
            required_monthly = remaining / months_to_deadline
            forecast["months_to_deadline"] = round(months_to_deadline, 1)
            forecast["required_monthly_contribution"] = required_monthly

    # Gerar projeção mês a mês
    monthly_projection = []
    current_total = contributed

    for i in range(1, projection_months + 1):
        projected_month = datetime.now() + timedelta(days=30*i)
        if monthly_avg > 0:
            current_total += monthly_avg
            goal_reached = current_total >= db_goal.target_amount

            monthly_projection.append({
                "month": projected_month.month,
                "year": projected_month.year,
                "projected_total": min(current_total, db_goal.target_amount),
                "projected_contribution": min(monthly_avg, db_goal.target_amount - (current_total - monthly_avg)),
                "percentage_complete": min(100, (current_total / db_goal.target_amount) * 100),
                "goal_reached": goal_reached
            })

            if goal_reached:
                break
        else:
            # Se não houver média, manter o valor atual
            monthly_projection.append({
                "month": projected_month.month,
                "year": projected_month.year,
                "projected_total": current_total,
                "projected_contribution": 0,
                "percentage_complete": (current_total / db_goal.target_amount) * 100,
                "goal_reached": current_total >= db_goal.target_amount
            })

    forecast["monthly_projection"] = monthly_projection
    return forecast

@router.get("/categories/summary")
def get_goals_categories_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retorna um resumo das metas por categoria.
    """
    # Buscar todas as metas do usuário
    goals = db.query(FinancialGoal).filter(
        FinancialGoal.user_id == current_user.id
    ).all()

    # Dicionário para armazenar os totais por categoria
    categories_summary = {}

    for goal in goals:
        category = goal.category or "Sem categoria"

        if category not in categories_summary:
            categories_summary[category] = {
                "total_goals": 0,
                "completed_goals": 0,
                "total_target": 0,
                "total_contributed": 0
            }

        # Incrementar estatísticas
        categories_summary[category]["total_goals"] += 1
        if goal.is_completed:
            categories_summary[category]["completed_goals"] += 1

        categories_summary[category]["total_target"] += goal.target_amount

        # Buscar total de contribuições para esta meta
        contributed = db.query(func.sum(Contribution.amount)).filter(
            Contribution.goal_id == goal.id
        ).scalar() or 0

        categories_summary[category]["total_contributed"] += contributed

    # Converter dicionário para lista para a resposta
    result = []
    for category, stats in categories_summary.items():
        # Calcular percentual de conclusão
        completion_percentage = 0
        if stats["total_target"] > 0:
            completion_percentage = (stats["total_contributed"] / stats["total_target"]) * 100

        result.append({
            "category": category,
            "total_goals": stats["total_goals"],
            "completed_goals": stats["completed_goals"],
            "completion_rate": (stats["completed_goals"] / stats["total_goals"]) * 100 if stats["total_goals"] > 0 else 0,
            "total_target": stats["total_target"],
            "total_contributed": stats["total_contributed"],
            "completion_percentage": completion_percentage
        })

    # Ordenar por total_target (decrescente)
    result.sort(key=lambda x: x["total_target"], reverse=True)

    return {"categories": result}