import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Analysis from './pages/Analysis';
import Planning from './pages/Planning';
import Documents from './pages/Documents';
import Family from './pages/Family';
import Diagnostic from './pages/Diagnostic';
import UserManagement from './pages/UserManagement';
import GoogleCallback from './pages/GoogleCallback';

interface PrivateRouteProps {
  children: React.ReactNode;
}

const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/auth/callback" element={<GoogleCallback />} />
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
      <Route
        path="/dashboard"
        element={
          <PrivateRoute>
            <Dashboard />
          </PrivateRoute>
        }
      />
      <Route
        path="/analysis"
        element={
          <PrivateRoute>
            <Analysis />
          </PrivateRoute>
        }
      />
      <Route
        path="/planning"
        element={
          <PrivateRoute>
            <Planning />
          </PrivateRoute>
        }
      />
      <Route
        path="/documents"
        element={
          <PrivateRoute>
            <Documents />
          </PrivateRoute>
        }
      />
      <Route
        path="/family"
        element={
          <PrivateRoute>
            <Family />
          </PrivateRoute>
        }
      />
      <Route
        path="/diagnostic"
        element={
          <PrivateRoute>
            <Diagnostic />
          </PrivateRoute>
        }
      />
      <Route
        path="/users"
        element={
          <PrivateRoute>
            <UserManagement />
          </PrivateRoute>
        }
      />
    </Routes>
  );
};

export default AppRoutes;