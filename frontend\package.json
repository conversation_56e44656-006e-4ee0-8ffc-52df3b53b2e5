{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@mui/x-date-pickers": "^7.23.6", "axios": "^1.7.9", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "lodash": "^4.17.21", "lucide-react": "^0.471.1", "papaparse": "^5.5.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^7.1.1", "recharts": "^2.15.3", "tesseract.js": "^4.1.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/lodash": "^4.17.17", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}