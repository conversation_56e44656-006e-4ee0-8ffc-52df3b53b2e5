import React, { createContext, useContext, useState, useCallback } from 'react';
import { GlobalFiltersData } from '../components/GlobalFilters/GlobalFilters';

interface FiltersContextData {
  filters: GlobalFiltersData;
  setFilters: (filters: GlobalFiltersData) => void;
  updateFilter: (field: keyof GlobalFiltersData, value: any) => void;
  clearFilters: () => void;
  hasActiveFilters: () => boolean;
  getFilterParams: () => URLSearchParams;
}

const FiltersContext = createContext<FiltersContextData>({} as FiltersContextData);

const defaultFilters: GlobalFiltersData = {
  startDate: null,
  endDate: null,
  type: '',
  category: '',
  familyMember: ''
};

export const FiltersProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [filters, setFiltersState] = useState<GlobalFiltersData>(() => {
    // Tentar carregar filtros do localStorage
    try {
      const savedFilters = localStorage.getItem('@FinanceApp:filters');
      if (savedFilters) {
        const parsed = JSON.parse(savedFilters);
        return {
          ...defaultFilters,
          ...parsed,
          startDate: parsed.startDate ? new Date(parsed.startDate) : null,
          endDate: parsed.endDate ? new Date(parsed.endDate) : null,
        };
      }
    } catch (error) {
      console.error('Erro ao carregar filtros do localStorage:', error);
    }
    return defaultFilters;
  });

  const setFilters = useCallback((newFilters: GlobalFiltersData) => {
    setFiltersState(newFilters);
    // Salvar no localStorage
    try {
      localStorage.setItem('@FinanceApp:filters', JSON.stringify({
        ...newFilters,
        startDate: newFilters.startDate?.toISOString(),
        endDate: newFilters.endDate?.toISOString(),
      }));
    } catch (error) {
      console.error('Erro ao salvar filtros no localStorage:', error);
    }
  }, []);

  const updateFilter = useCallback((field: keyof GlobalFiltersData, value: any) => {
    setFilters({
      ...filters,
      [field]: value
    });
  }, [filters, setFilters]);

  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, [setFilters]);

  const hasActiveFilters = useCallback(() => {
    return !!(
      filters.startDate ||
      filters.endDate ||
      filters.type ||
      filters.category ||
      filters.familyMember
    );
  }, [filters]);

  const getFilterParams = useCallback(() => {
    const params = new URLSearchParams();
    
    if (filters.startDate) {
      params.append('start_date', filters.startDate.toISOString());
    }
    
    if (filters.endDate) {
      params.append('end_date', filters.endDate.toISOString());
    }
    
    if (filters.type) {
      params.append('type', filters.type);
    }
    
    if (filters.category) {
      params.append('category', filters.category);
    }
    
    if (filters.familyMember) {
      if (filters.familyMember === 'self') {
        // Para 'self', não enviamos o parâmetro family_member_id
        // O backend retornará apenas as transações do usuário atual
      } else {
        params.append('family_member_id', filters.familyMember);
      }
    }
    
    return params;
  }, [filters]);

  return (
    <FiltersContext.Provider
      value={{
        filters,
        setFilters,
        updateFilter,
        clearFilters,
        hasActiveFilters,
        getFilterParams
      }}
    >
      {children}
    </FiltersContext.Provider>
  );
};

export const useFilters = () => {
  const context = useContext(FiltersContext);
  if (!context) {
    throw new Error('useFilters must be used within a FiltersProvider');
  }
  return context;
};

export default FiltersContext;
