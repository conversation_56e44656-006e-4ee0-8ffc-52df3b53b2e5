import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  CircularProgress,
  Al<PERSON>,
  Button
} from '@mui/material';
import { CheckCircle, Error } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const GoogleCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();
  const { signIn } = useAuth();

  useEffect(() => {
    const processCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (error) {
        setStatus('error');
        setMessage('Login cancelado ou erro na autenticação com Google.');
        return;
      }

      if (!code || !state) {
        setStatus('error');
        setMessage('Parâmetros inválidos recebidos do Google.');
        return;
      }

      try {
        const response = await api.get(`/auth/google/callback?code=${code}&state=${state}`);
        
        if (response.data.access_token) {
          signIn(response.data.access_token);
          setStatus('success');
          setMessage('Login realizado com sucesso! Redirecionando...');
          
          // Redirecionar após 2 segundos
          setTimeout(() => {
            navigate('/dashboard');
          }, 2000);
        } else {
          setStatus('error');
          setMessage('Erro ao processar login com Google.');
        }
      } catch (err: any) {
        console.error('Erro no callback do Google:', err);
        setStatus('error');
        setMessage(
          err.response?.data?.detail || 
          'Erro ao processar login com Google. Tente novamente.'
        );
      }
    };

    processCallback();
  }, [searchParams, signIn, navigate]);

  const handleRetry = () => {
    navigate('/login');
  };

  return (
    <Container maxWidth="sm" sx={{ mt: 8 }}>
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        {status === 'loading' && (
          <>
            <CircularProgress size={60} sx={{ mb: 3 }} />
            <Typography variant="h5" gutterBottom>
              Processando login...
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Aguarde enquanto validamos suas credenciais do Google.
            </Typography>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle sx={{ fontSize: 60, color: 'success.main', mb: 3 }} />
            <Typography variant="h5" gutterBottom color="success.main">
              Login realizado com sucesso!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {message}
            </Typography>
          </>
        )}

        {status === 'error' && (
          <>
            <Error sx={{ fontSize: 60, color: 'error.main', mb: 3 }} />
            <Typography variant="h5" gutterBottom color="error.main">
              Erro no login
            </Typography>
            <Alert severity="error" sx={{ mb: 3 }}>
              {message}
            </Alert>
            <Button
              variant="contained"
              onClick={handleRetry}
              sx={{ mt: 2 }}
            >
              Tentar novamente
            </Button>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleCallback;
