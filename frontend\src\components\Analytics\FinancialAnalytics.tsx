import React, { useState } from 'react';
import { Box, Tab, Tabs, Paper, Typography } from '@mui/material';
import GeneralAnalytics from './GeneralAnalytics';
// Importações temporariamente comentadas para resolver erros de compilação
// import ExpenseAnalytics from './ExpenseAnalytics';
// import IncomeAnalytics from './IncomeAnalytics';
import InvestmentSummary from './InvestmentSummary';
import { AISpendingLimits } from '../AI';

interface FinancialAnalyticsProps {
  transactions: any[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
      sx={{ pt: 3 }}
    >
      {value === index && children}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `analytics-tab-${index}`,
    'aria-controls': `analytics-tabpanel-${index}`,
  };
}

// Componentes temporários até que os módulos sejam reconhecidos corretamente
const ExpenseAnalytics = ({ expenses }: { expenses: any[] }) => {
  // Filtrar despesas excluindo compras no cartão e reembolsos (seguindo a mesma lógica do Dashboard)
  const filteredExpenses = expenses.filter(expense =>
    expense.subtype !== 'credit_card_purchase' &&
    expense.subtype !== 'refund'
  );

  const totalExpenses = filteredExpenses.reduce((sum, exp) => sum + exp.amount, 0);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Análise de Despesas
      </Typography>
      <Typography>
        {filteredExpenses.length} despesas encontradas (excluindo compras no cartão de crédito). Total: {new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(totalExpenses)}
      </Typography>
    </Box>
  );
};

const IncomeAnalytics = ({ incomes }: { incomes: any[] }) => (
  <Box>
    <Typography variant="h6" gutterBottom>
      Análise de Receitas
    </Typography>
    <Typography>
      {incomes.length} receitas encontradas. Total: {new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(incomes.reduce((sum, inc) => sum + inc.amount, 0))}
    </Typography>
  </Box>
);

const FinancialAnalytics: React.FC<FinancialAnalyticsProps> = ({ transactions }) => {
  const [value, setValue] = useState(0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  // Filtrar transações por tipo
  const expenses = transactions.filter(t => t.type === 'expense');
  const incomes = transactions.filter(t => t.type === 'income');
  const investments = transactions.filter(t => t.type === 'investment');

  return (
    <Box>
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={value}
          onChange={handleChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Geral" {...a11yProps(0)} />
          <Tab label="Despesas" {...a11yProps(1)} />
          <Tab label="Receitas" {...a11yProps(2)} />
          <Tab label="Investimentos" {...a11yProps(3)} />
          <Tab label="Limites" {...a11yProps(4)} />
        </Tabs>
      </Paper>

      <TabPanel value={value} index={0}>
        <GeneralAnalytics transactions={transactions} />
      </TabPanel>

      <TabPanel value={value} index={1}>
        <Paper sx={{ p: 4 }}>
          <ExpenseAnalytics expenses={expenses} />
        </Paper>
      </TabPanel>

      <TabPanel value={value} index={2}>
        <Paper sx={{ p: 4 }}>
          <IncomeAnalytics incomes={incomes} />
        </Paper>
      </TabPanel>

      <TabPanel value={value} index={3}>
        <Paper sx={{ p: 4 }}>
          <InvestmentSummary investments={investments} />
        </Paper>
      </TabPanel>

      <TabPanel value={value} index={4}>
        <AISpendingLimits />
      </TabPanel>
    </Box>
  );
};

export default FinancialAnalytics;