import api from './api';

// Types
export interface AIStatus {
  enabled: boolean;
  available: boolean;
  provider: string;
  message: string;
}

export interface Insight {
  title: string;
  explanation: string;
}

export interface InsightsResponse {
  message: string;
  insights: Insight[];
  summary: {
    total_income: number;
    total_expenses: number;
    balance: number;
  };
}

export interface Transaction {
  id: number;
  description: string;
  amount: number;
  suggested_category?: string;
}

export interface CategoryResponse {
  message: string;
  transactions: Transaction[];
}

export interface SavingsGoal {
  target_amount?: number;
  target_date?: string;
}

export interface Recommendation {
  title: string;
  explanation: string;
  estimated_savings: number;
}

export interface RecommendationsResponse {
  message: string;
  recommendations: Recommendation[];
  current_spending_patterns: Record<string, number>;
}

export interface CategoryLimit {
  category: string;
  limit: number;
  justification?: string;
}

export interface LimitsResponse {
  message: string;
  limits: CategoryLimit[];
  spending_statistics?: any[];
}

// AI Service functions
const aiService = {
  // Check AI module status
  getStatus: async (): Promise<AIStatus> => {
    const response = await api.get('/ai/status');
    return response.data;
  },

  // Get financial insights
  getInsights: async (timePeriod: string = 'month'): Promise<InsightsResponse> => {
    const response = await api.get(`/ai/insights?time_period=${timePeriod}`);
    return response.data;
  },

  // Categorize transactions
  categorizeTransactions: async (transactions: Omit<Transaction, 'suggested_category'>[]): Promise<CategoryResponse> => {
    const response = await api.post('/ai/categorize-transactions', transactions);
    return response.data;
  },

  // Get savings recommendations
  getSavingsRecommendations: async (goal?: SavingsGoal): Promise<RecommendationsResponse> => {
    const response = await api.post('/ai/savings-recommendations', goal || {});
    return response.data;
  },

  // Get spending limits
  getSpendingLimits: async (): Promise<LimitsResponse> => {
    const response = await api.get('/ai/spending-limits');
    return response.data;
  },

  // Set spending limits
  setSpendingLimits: async (limits: CategoryLimit[]): Promise<LimitsResponse> => {
    const response = await api.post('/ai/set-limits', limits);
    return response.data;
  }
};

export default aiService;
