import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert
} from '@mui/material';
import { Lightbulb as LightbulbIcon, Save as SaveIcon } from '@mui/icons-material';
import aiService, { CategoryLimit } from '../../services/aiService';

interface AISpendingLimitsProps {
  onFeedback?: (message: string, severity: 'success' | 'info' | 'warning' | 'error') => void;
}

const AISpendingLimits: React.FC<AISpendingLimitsProps> = ({ onFeedback }) => {
  const [limits, setLimits] = useState<CategoryLimit[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);

  const suggestLimits = async () => {
    setLoading(true);
    setError(null);
    setShowForm(false);
    
    try {
      const response = await aiService.getSpendingLimits();
      setLimits(response.limits);
      
      if (response.limits.length > 0) {
        setShowForm(true);
        if (onFeedback) {
          onFeedback('Limites de gastos sugeridos com sucesso!', 'success');
        }
      } else {
        if (onFeedback) {
          onFeedback('Não há dados suficientes para sugerir limites.', 'info');
        }
      }
    } catch (err) {
      console.error('Erro ao sugerir limites:', err);
      setError('Não foi possível sugerir limites. Por favor, tente novamente.');
      if (onFeedback) {
        onFeedback('Erro ao sugerir limites de gastos.', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLimitChange = (index: number, value: string) => {
    const newLimits = [...limits];
    newLimits[index] = {
      ...newLimits[index],
      limit: parseFloat(value) || 0
    };
    setLimits(newLimits);
  };

  const saveLimits = async () => {
    setSaving(true);
    setError(null);
    
    try {
      await aiService.setSpendingLimits(limits);
      
      if (onFeedback) {
        onFeedback('Limites de gastos salvos com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao salvar limites:', err);
      setError('Não foi possível salvar os limites. Por favor, tente novamente.');
      if (onFeedback) {
        onFeedback('Erro ao salvar limites de gastos.', 'error');
      }
    } finally {
      setSaving(false);
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" component="h2" sx={{ display: 'flex', alignItems: 'center' }}>
          Limites de Gastos
          <Box
            component="span"
            sx={{
              ml: 1,
              fontSize: '0.7rem',
              bgcolor: '#E1F5FE',
              color: '#0288D1',
              p: '0.2rem 0.4rem',
              borderRadius: '4px',
              fontWeight: 'normal'
            }}
          >
            IA
          </Box>
        </Typography>
        <Button
          variant="contained"
          startIcon={<LightbulbIcon />}
          onClick={suggestLimits}
          disabled={loading || saving}
        >
          Sugerir Limites
        </Button>
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Analisando seu histórico de gastos...
          </Typography>
        </Box>
      )}

      {error && !loading && !saving && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && !showForm && limits.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body2" color="text.secondary">
            Clique em "Sugerir Limites" para obter recomendações baseadas no seu histórico.
          </Typography>
        </Box>
      )}

      {!loading && !error && showForm && limits.length > 0 && (
        <>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Categoria</TableCell>
                  <TableCell>Limite (R$)</TableCell>
                  <TableCell>Justificativa</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {limits.map((limit, index) => (
                  <TableRow 
                    key={index}
                    hover
                    sx={{ '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.03)' } }}
                  >
                    <TableCell>
                      {limit.category.charAt(0).toUpperCase() + limit.category.slice(1)}
                    </TableCell>
                    <TableCell>
                      <TextField
                        type="number"
                        value={limit.limit}
                        onChange={(e) => handleLimitChange(index, e.target.value)}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                          inputProps: { min: 0, step: 0.01 }
                        }}
                        size="small"
                        sx={{ width: '150px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                        {limit.justification}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="contained"
              color="success"
              startIcon={<SaveIcon />}
              onClick={saveLimits}
              disabled={saving}
            >
              {saving ? 'Salvando...' : 'Salvar Limites'}
            </Button>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default AISpendingLimits;
