from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, extract, cast, Date
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta, date
from calendar import monthrange
import calendar
from ..database import get_db
from ..models.financial import (
    User, Account, Transaction, Budget, Category,
    RecurringTransaction, CreditCard, CreditCardStatement
)
from ..security import get_current_user
import logging

# Configurar logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"],
    responses={404: {"description": "Not found"}}
)

@router.get("/summary")
def get_financial_summary(
    current_user: User = Depends(get_current_user),
    period: str = Query("month", description="Período para o resumo: 'day', 'week', 'month', 'year'"),
    db: Session = Depends(get_db)
):
    """
    Retorna um resumo financeiro geral do usuário, incluindo saldo total,
    receitas, despesas e transações recentes.
    """
    today = datetime.now().date()

    # Definir o período para filtrar transações
    if period == "day":
        start_date = today
        period_name = "Hoje"
    elif period == "week":
        start_date = today - timedelta(days=today.weekday())
        period_name = "Esta Semana"
    elif period == "month":
        start_date = date(today.year, today.month, 1)
        period_name = f"{calendar.month_name[today.month]} de {today.year}"
    elif period == "year":
        start_date = date(today.year, 1, 1)
        period_name = str(today.year)
    else:
        start_date = date(today.year, today.month, 1)
        period_name = f"{calendar.month_name[today.month]} de {today.year}"

    end_date = today

    # Calcular saldo total em contas
    accounts = db.query(Account).filter(Account.user_id == current_user.id).all()
    total_balance = sum(account.current_balance for account in accounts)

    # Calcular o total de cartões de crédito
    credit_cards = db.query(CreditCard).filter(CreditCard.user_id == current_user.id).all()
    total_credit_card_balance = sum(card.current_balance for card in credit_cards)

    # Calcular totais de receitas e despesas no período
    income_sum = db.query(func.sum(Transaction.amount)).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "income",
        cast(Transaction.date, Date) >= start_date,
        cast(Transaction.date, Date) <= end_date
    ).scalar() or 0.0

    expense_sum = db.query(func.sum(Transaction.amount)).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "expense",
        cast(Transaction.date, Date) >= start_date,
        cast(Transaction.date, Date) <= end_date
    ).scalar() or 0.0

    # Transações recentes
    recent_transactions = db.query(Transaction).filter(
        Transaction.user_id == current_user.id
    ).order_by(Transaction.date.desc()).limit(5).all()

    # Faturas de cartão de crédito a vencer
    upcoming_statements = db.query(CreditCardStatement).join(
        CreditCard, CreditCardStatement.credit_card_id == CreditCard.id
    ).filter(
        CreditCard.user_id == current_user.id,
        CreditCardStatement.due_date >= today,
        CreditCardStatement.due_date <= today + timedelta(days=30),
        CreditCardStatement.is_paid == False
    ).all()

    # Formatando as transações recentes
    recent_transactions_formatted = []
    for transaction in recent_transactions:
        recent_transactions_formatted.append({
            "id": transaction.id,
            "description": transaction.description,
            "amount": transaction.amount,
            "type": transaction.type,
            "date": transaction.date,
            "category": transaction.category
        })

    # Formatando as faturas a vencer
    upcoming_statements_formatted = []
    for statement in upcoming_statements:
        credit_card = db.query(CreditCard).filter(CreditCard.id == statement.credit_card_id).first()
        upcoming_statements_formatted.append({
            "id": statement.id,
            "credit_card_id": statement.credit_card_id,
            "credit_card_name": credit_card.name if credit_card else "Desconhecido",
            "due_date": statement.due_date,
            "total_amount": statement.total_amount,
            "paid_amount": statement.paid_amount,
            "remaining_amount": statement.total_amount - statement.paid_amount,
            "days_to_due": (statement.due_date.date() - today).days
        })

    return {
        "period": {
            "name": period_name,
            "start_date": start_date,
            "end_date": end_date
        },
        "accounts": {
            "count": len(accounts),
            "total_balance": total_balance
        },
        "credit_cards": {
            "count": len(credit_cards),
            "total_balance": total_credit_card_balance
        },
        "transactions": {
            "income": income_sum,
            "expense": expense_sum,
            "net": income_sum - expense_sum,
            "recent": recent_transactions_formatted
        },
        "upcoming_statements": {
            "count": len(upcoming_statements_formatted),
            "total_due": sum(s["remaining_amount"] for s in upcoming_statements_formatted),
            "statements": upcoming_statements_formatted
        }
    }

@router.get("/expenses-by-category")
def get_expenses_by_category(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Retorna as despesas agrupadas por categoria em um período específico.
    """
    # Se as datas não forem fornecidas, usar o mês atual
    if not start_date:
        today = datetime.now().date()
        start_date = date(today.year, today.month, 1)

    if not end_date:
        today = datetime.now().date()
        # Último dia do mês atual
        last_day = monthrange(today.year, today.month)[1]
        end_date = date(today.year, today.month, last_day)

    # Consulta para obter as despesas por categoria
    expenses_by_category = db.query(
        Transaction.category,
        func.sum(Transaction.amount).label("total")
    ).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "expense",
        cast(Transaction.date, Date) >= start_date,
        cast(Transaction.date, Date) <= end_date
    ).group_by(Transaction.category).all()

    # Formatar o resultado
    result = []
    for category, total in expenses_by_category:
        result.append({
            "category": category,
            "total": float(total)
        })

    # Ordenar por total (maior para menor)
    result.sort(key=lambda x: x["total"], reverse=True)

    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "expenses_by_category": result,
        "total": sum(item["total"] for item in result)
    }

@router.get("/income-by-category")
def get_income_by_category(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Retorna as receitas agrupadas por categoria em um período específico.
    """
    # Se as datas não forem fornecidas, usar o mês atual
    if not start_date:
        today = datetime.now().date()
        start_date = date(today.year, today.month, 1)

    if not end_date:
        today = datetime.now().date()
        # Último dia do mês atual
        last_day = monthrange(today.year, today.month)[1]
        end_date = date(today.year, today.month, last_day)

    # Consulta para obter as receitas por categoria
    income_by_category = db.query(
        Transaction.category,
        func.sum(Transaction.amount).label("total")
    ).filter(
        Transaction.user_id == current_user.id,
        Transaction.type == "income",
        cast(Transaction.date, Date) >= start_date,
        cast(Transaction.date, Date) <= end_date
    ).group_by(Transaction.category).all()

    # Formatar o resultado
    result = []
    for category, total in income_by_category:
        result.append({
            "category": category,
            "total": float(total)
        })

    # Ordenar por total (maior para menor)
    result.sort(key=lambda x: x["total"], reverse=True)

    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "income_by_category": result,
        "total": sum(item["total"] for item in result)
    }

@router.get("/monthly-trend")
def get_monthly_trend(
    current_user: User = Depends(get_current_user),
    months: int = Query(6, description="Número de meses para calcular a tendência"),
    db: Session = Depends(get_db)
):
    """
    Retorna a tendência de receitas e despesas dos últimos X meses.
    """
    today = datetime.now().date()
    result = []

    # Calcular para cada mês
    for i in range(months - 1, -1, -1):
        # Calcular o mês
        if today.month - i <= 0:
            year = today.year - 1
            month = 12 + (today.month - i)
        else:
            year = today.year
            month = today.month - i

        # Primeiro e último dia do mês
        first_day = date(year, month, 1)
        last_day = date(year, month, monthrange(year, month)[1])

        # Calcular receitas
        income_sum = db.query(func.sum(Transaction.amount)).filter(
            Transaction.user_id == current_user.id,
            Transaction.type == "income",
            extract('year', Transaction.date) == year,
            extract('month', Transaction.date) == month
        ).scalar() or 0.0

        # Calcular despesas
        expense_sum = db.query(func.sum(Transaction.amount)).filter(
            Transaction.user_id == current_user.id,
            Transaction.type == "expense",
            extract('year', Transaction.date) == year,
            extract('month', Transaction.date) == month
        ).scalar() or 0.0

        result.append({
            "year": year,
            "month": month,
            "month_name": calendar.month_name[month],
            "period": f"{calendar.month_name[month]}/{year}",
            "income": float(income_sum),
            "expense": float(expense_sum),
            "net": float(income_sum - expense_sum)
        })

    return {
        "monthly_trend": result,
        "total_income": sum(month["income"] for month in result),
        "total_expense": sum(month["expense"] for month in result),
        "total_net": sum(month["net"] for month in result),
        "average_monthly_income": sum(month["income"] for month in result) / len(result) if result else 0,
        "average_monthly_expense": sum(month["expense"] for month in result) / len(result) if result else 0
    }

@router.get("/budget-status")
def get_budget_status(
    current_user: User = Depends(get_current_user),
    month: int = Query(None, description="Mês (1-12)"),
    year: int = Query(None, description="Ano"),
    db: Session = Depends(get_db)
):
    """
    Retorna o status de todos os orçamentos do usuário para um mês específico.
    """
    # Se mês e ano não forem fornecidos, usar o mês atual
    today = datetime.now().date()
    month = month or today.month
    year = year or today.year

    # Primeiro e último dia do mês
    first_day = date(year, month, 1)
    last_day = date(year, month, monthrange(year, month)[1])

    # Buscar todos os orçamentos do usuário
    budgets = db.query(Budget).filter(
        Budget.user_id == current_user.id,
        Budget.start_date <= last_day,
        or_(
            Budget.end_date >= first_day,
            Budget.end_date == None
        )
    ).all()

    result = []
    for budget in budgets:
        # Calcular o gasto atual na categoria
        expense_sum = db.query(func.sum(Transaction.amount)).filter(
            Transaction.user_id == current_user.id,
            Transaction.type == "expense",
            Transaction.category == budget.category,
            cast(Transaction.date, Date) >= first_day,
            cast(Transaction.date, Date) <= last_day
        ).scalar() or 0.0

        # Calcular a porcentagem utilizada
        percentage_used = (expense_sum / budget.amount) * 100 if budget.amount > 0 else 0

        result.append({
            "id": budget.id,
            "category": budget.category,
            "budgeted_amount": budget.amount,
            "spent_amount": float(expense_sum),
            "remaining_amount": budget.amount - float(expense_sum),
            "percentage_used": float(percentage_used),
            "status": "exceeded" if expense_sum > budget.amount else "within_budget"
        })

    return {
        "period": {
            "month": month,
            "month_name": calendar.month_name[month],
            "year": year,
            "start_date": first_day,
            "end_date": last_day
        },
        "budgets": result,
        "total_budgeted": sum(budget.amount for budget in budgets),
        "total_spent": sum(item["spent_amount"] for item in result),
        "total_remaining": sum(item["remaining_amount"] for item in result),
        "overall_percentage_used": (sum(item["spent_amount"] for item in result) / sum(budget.amount for budget in budgets) * 100) if budgets and sum(budget.amount for budget in budgets) > 0 else 0
    }

@router.get("/cash-flow")
def get_cash_flow(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    group_by: str = Query("day", description="Como agrupar os dados: 'day', 'week', 'month'"),
    db: Session = Depends(get_db)
):
    """
    Retorna o fluxo de caixa (entradas e saídas) agrupadas por dia, semana ou mês.
    """
    # Se as datas não forem fornecidas, usar o mês atual
    today = datetime.now().date()

    if not start_date:
        start_date = date(today.year, today.month, 1)

    if not end_date:
        # Último dia do mês atual
        last_day = monthrange(today.year, today.month)[1]
        end_date = date(today.year, today.month, last_day)

    result = []

    if group_by == "day":
        # Agrupar por dia
        days = (end_date - start_date.date()).days + 1
        for day_offset in range(days):
            day = start_date.date() + timedelta(days=day_offset)

            # Calcular receitas
            income_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "income",
                cast(Transaction.date, Date) == day
            ).scalar() or 0.0

            # Calcular despesas
            expense_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "expense",
                cast(Transaction.date, Date) == day
            ).scalar() or 0.0

            result.append({
                "date": day,
                "period": day.strftime("%d/%m/%Y"),
                "income": float(income_sum),
                "expense": float(expense_sum),
                "net": float(income_sum - expense_sum)
            })

    elif group_by == "week":
        # Agrupar por semana
        current_week_start = start_date.date() - timedelta(days=start_date.date().weekday())

        # Iterar por semanas
        while current_week_start <= end_date.date():
            current_week_end = current_week_start + timedelta(days=6)

            # Ajustar a data de fim da semana se exceder a data final
            if current_week_end > end_date.date():
                current_week_end = end_date.date()

            # Calcular receitas
            income_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "income",
                cast(Transaction.date, Date) >= current_week_start,
                cast(Transaction.date, Date) <= current_week_end
            ).scalar() or 0.0

            # Calcular despesas
            expense_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "expense",
                cast(Transaction.date, Date) >= current_week_start,
                cast(Transaction.date, Date) <= current_week_end
            ).scalar() or 0.0

            result.append({
                "start_date": current_week_start,
                "end_date": current_week_end,
                "period": f"{current_week_start.strftime('%d/%m')} - {current_week_end.strftime('%d/%m/%Y')}",
                "income": float(income_sum),
                "expense": float(expense_sum),
                "net": float(income_sum - expense_sum)
            })

            current_week_start += timedelta(days=7)

    elif group_by == "month":
        # Agrupar por mês
        current_date = start_date.date().replace(day=1)

        while current_date <= end_date.date():
            current_month = current_date.month
            current_year = current_date.year
            month_end = date(current_year, current_month, monthrange(current_year, current_month)[1])

            # Ajustar a data de fim do mês se exceder a data final
            if month_end > end_date.date():
                month_end = end_date.date()

            # Calcular receitas
            income_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "income",
                extract('year', Transaction.date) == current_year,
                extract('month', Transaction.date) == current_month
            ).scalar() or 0.0

            # Calcular despesas
            expense_sum = db.query(func.sum(Transaction.amount)).filter(
                Transaction.user_id == current_user.id,
                Transaction.type == "expense",
                extract('year', Transaction.date) == current_year,
                extract('month', Transaction.date) == current_month
            ).scalar() or 0.0

            result.append({
                "start_date": current_date,
                "end_date": month_end,
                "period": f"{calendar.month_name[current_month]}/{current_year}",
                "income": float(income_sum),
                "expense": float(expense_sum),
                "net": float(income_sum - expense_sum)
            })

            # Avançar para o próximo mês
            if current_month == 12:
                next_year = current_year + 1
                next_month = 1
            else:
                next_year = current_year
                next_month = current_month + 1

            current_date = date(next_year, next_month, 1)

    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date,
            "group_by": group_by
        },
        "cash_flow": result,
        "summary": {
            "total_income": sum(item["income"] for item in result),
            "total_expense": sum(item["expense"] for item in result),
            "total_net": sum(item["net"] for item in result)
        }
    }

@router.get("/recurring-transactions")
def get_recurring_transactions_summary(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Retorna um resumo de todas as transações recorrentes ativas e seu impacto financeiro.
    """
    # Se as datas não forem fornecidas, usar o mês atual e os próximos 2 meses
    today = datetime.now().date()
    if not start_date:
        start_date = today

    if not end_date:
        end_date = today + timedelta(days=90)  # Próximos 3 meses

    # Buscar todas as transações recorrentes ativas
    recurring_transactions = db.query(RecurringTransaction).filter(
        RecurringTransaction.user_id == current_user.id,
        RecurringTransaction.is_active == True,
        or_(
            RecurringTransaction.end_date.is_(None),
            RecurringTransaction.end_date >= start_date
        )
    ).all()

    result = []
    total_income = 0
    total_expense = 0

    for rec_transaction in recurring_transactions:
        # Calcular quantas ocorrências esperadas entre as datas
        start = max(start_date.date(), rec_transaction.start_date)
        end = min(end_date.date(), rec_transaction.end_date) if rec_transaction.end_date else end_date.date()

        # Calcular o número esperado de ocorrências baseado na frequência
        occurrences = []

        current_date = start
        while current_date <= end:
            # Verificar se a data atual corresponde ao padrão de recorrência
            include_occurrence = False

            if rec_transaction.frequency == "daily":
                include_occurrence = True

            elif rec_transaction.frequency == "weekly":
                # Verificar se o dia da semana é o mesmo da data de início
                if current_date.weekday() == rec_transaction.start_date.weekday():
                    include_occurrence = True

            elif rec_transaction.frequency == "monthly":
                # Verificar se o dia do mês é o mesmo da data de início
                # Tomar cuidado com meses com menos dias
                target_day = min(rec_transaction.start_date.day, monthrange(current_date.year, current_date.month)[1])
                if current_date.day == target_day:
                    include_occurrence = True

            elif rec_transaction.frequency == "yearly":
                # Verificar se o dia e mês são os mesmos da data de início
                if current_date.day == rec_transaction.start_date.day and current_date.month == rec_transaction.start_date.month:
                    include_occurrence = True

            if include_occurrence:
                occurrences.append(current_date)

            current_date += timedelta(days=1)

        # Calcular impacto financeiro
        financial_impact = rec_transaction.amount * len(occurrences)

        # Categorizar como receita ou despesa
        if rec_transaction.type == "income":
            total_income += financial_impact
        else:
            total_expense += financial_impact

        result.append({
            "id": rec_transaction.id,
            "description": rec_transaction.description,
            "amount": rec_transaction.amount,
            "type": rec_transaction.type,
            "category": rec_transaction.category,
            "frequency": rec_transaction.frequency,
            "next_occurrence": occurrences[0] if occurrences else None,
            "occurrences_in_period": len(occurrences),
            "financial_impact": financial_impact,
            "expected_dates": [date.strftime("%d/%m/%Y") for date in occurrences]
        })

    # Ordenar por próxima ocorrência
    result.sort(key=lambda x: x["next_occurrence"] or date(9999, 12, 31))

    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "recurring_transactions": result,
        "summary": {
            "count": len(recurring_transactions),
            "total_income": total_income,
            "total_expense": total_expense,
            "net_impact": total_income - total_expense
        }
    }