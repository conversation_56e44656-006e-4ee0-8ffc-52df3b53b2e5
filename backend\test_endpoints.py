#!/usr/bin/env python3
"""
Script para testar endpoints específicos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import httpx
import json

async def test_endpoints():
    """Testa os endpoints problemáticos"""
    
    base_url = "http://localhost:8000"
    
    # 1. Fazer login
    print("1. Testando login...")
    login_data = {
        "username": "<EMAIL>",
        "password": "admin123"
    }
    
    async with httpx.AsyncClient() as client:
        # Login
        response = await client.post(
            f"{base_url}/api/v1/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code != 200:
            print(f"❌ Login falhou: {response.status_code} - {response.text}")
            return
        
        token_data = response.json()
        token = token_data["access_token"]
        print(f"✅ Login bem-sucedido! Token: {token[:50]}...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. Testar endpoint /me
        print("\n2. Testando endpoint /me...")
        response = await client.get(f"{base_url}/api/v1/auth/users/me", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # 3. Testar budgets
        print("\n3. Testando endpoint budgets...")
        response = await client.get(f"{base_url}/api/v1/budgets/", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # 4. Testar financial-goals
        print("\n4. Testando endpoint financial-goals...")
        response = await client.get(f"{base_url}/api/v1/financial-goals/", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # 5. Testar investments
        print("\n5. Testando endpoint investments...")
        response = await client.get(f"{base_url}/api/v1/investments/", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # 6. Testar transactions/summary (problema de método)
        print("\n6. Testando endpoint transactions/summary...")
        response = await client.get(f"{base_url}/api/v1/transactions/summary", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    asyncio.run(test_endpoints())
