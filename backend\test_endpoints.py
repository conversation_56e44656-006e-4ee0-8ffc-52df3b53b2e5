#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

import requests
import json

def test_endpoints():
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 TESTING ALL CRITICAL ENDPOINTS")
    print("=" * 50)
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health endpoint: {response.status_code}")
        
        # Test login
        login_data = {
            'username': '<EMAIL>',
            'password': 'admin123'
        }
        
        response = requests.post(f"{base_url}/auth/token", data=login_data, timeout=10)
        print(f"Login: {response.status_code}")
        
        if response.status_code == 200:
            token = response.json()['access_token']
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test protected endpoints
            endpoints = [
                ('/auth/users/me', 'User info'),
                ('/financial-goals/', 'Financial goals'),
                ('/budgets/', 'Budgets'),
                ('/investments/', 'Investments'),
            ]
            
            for endpoint, name in endpoints:
                try:
                    response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
                    status = "✅" if response.status_code == 200 else "❌"
                    print(f"{status} {name}: {response.status_code}")
                    if response.status_code != 200:
                        print(f"   Error: {response.text[:100]}")
                except Exception as e:
                    print(f"❌ {name}: ERROR - {str(e)}")
            
            # Test transaction creation
            transaction_data = {
                'description': 'Teste Investimento',
                'amount': 1000.0,
                'type': 'investment',
                'category': 'Investimentos',
                'date': '2025-01-20',
                'subtype': 'aplicacao'
            }
            
            try:
                response = requests.post(f"{base_url}/transactions/", 
                                       json=transaction_data, 
                                       headers=headers, 
                                       timeout=10)
                status = "✅" if response.status_code == 200 else "❌"
                print(f"{status} Create investment transaction: {response.status_code}")
                if response.status_code != 200:
                    print(f"   Error: {response.text[:100]}")
            except Exception as e:
                print(f"❌ Create transaction: ERROR - {str(e)}")
        
        print("\n" + "=" * 50)
        print("Test completed!")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        print("Make sure the server is running on http://localhost:8000")

if __name__ == "__main__":
    test_endpoints()
