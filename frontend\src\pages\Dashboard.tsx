import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  AppBar,
  Toolbar,
  IconButton,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Fab,
  Snackbar,
  Alert,
  Tooltip,
  Tab,
  Tabs,
  Checkbox,
  Container,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ExitToApp as LogoutIcon,
  AccountBalance as BalanceIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CreditCard as CreditCardIcon,
  ShowChart as InvestmentIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import TransactionForm from '../components/Transactions/TransactionForm';
import TransactionFilters, { TransactionFiltersProps } from '../components/Transactions/TransactionFilters';
import DeleteTransactionDialog from '../components/Transactions/DeleteTransactionDialog';
import FinancialAnalytics from '../components/Analytics/FinancialAnalytics';
import DocumentProcessor from '../components/Analytics/DocumentProcessor';
import TestUploadAuth from '../components/Analytics/TestUploadAuth';
import FinancialGoalList from '../components/Goals/FinancialGoalList';
import BudgetManager from '../components/Goals/BudgetManager';
// Import AI components
import { AIInsights, AISavingsRecommendations } from '../components/AI';

// Rótulos em português para os subtipos
const SUBTYPE_LABELS: Record<string, string> = {
  'purchase': 'Compra',
  'refund': 'Estorno/Reembolso',
  'payment': 'Pagamento',
  'transfer_in': 'Transferência Recebida',
  'transfer_out': 'Transferência Enviada',
  'investment_deposit': 'Aplicação',
  'investment_withdrawal': 'Resgate',
  'credit_card_payment': 'Pagamento de Fatura',
  'credit_card_purchase': 'Compra no Cartão',
  'tithe': 'Dízimo',
  'donation': 'Doação',
  'other': 'Outro'
};

interface Transaction {
  id: number;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: string;
  subtype: string;
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  is_investment?: boolean;
  is_refund?: boolean; // Flag para identificar estornos
}

interface TransactionFormData {
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: Date;
  subtype: 'purchase' | 'refund' | 'payment' | 'transfer_in' | 'transfer_out' | 'investment_deposit' | 'investment_withdrawal' | 'credit_card_payment' | 'credit_card_purchase' | 'tithe' | 'donation' | 'other';
}

interface SummaryCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
      sx={{ width: '100%' }}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </Box>
  );
}

const Dashboard: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isTransactionFormOpen, setIsTransactionFormOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [filters, setFilters] = useState<TransactionFiltersProps['filters']>({
    startDate: null,
    endDate: null,
    type: '',
    category: ''
  });
  const [feedback, setFeedback] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });
  const [isAdmin, setIsAdmin] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    transactionId: null as number | null
  });
  const [editingTransaction, setEditingTransaction] = useState<Transaction | undefined>(undefined);
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>([]);
  const { signOut } = useAuth();
  const navigate = useNavigate();

  const handleSelectTransaction = (transactionId: number) => {
    setSelectedTransactions(prev =>
      prev.includes(transactionId)
        ? prev.filter(id => id !== transactionId)
        : [...prev, transactionId]
    );
  };

  const handleSelectAllVisible = () => {
    const visibleIds = filteredTransactions.map(t => t.id);
    setSelectedTransactions(prev =>
      prev.length === visibleIds.length ? [] : visibleIds
    );
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(selectedTransactions.map(id => api.delete(`/transactions/${id}`)));
      await loadTransactions();
      setSelectedTransactions([]);
      setFeedback({
        open: true,
        message: 'Transações excluídas com sucesso!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erro ao excluir transações:', error);
      setFeedback({
        open: true,
        message: 'Erro ao excluir transações. Tente novamente.',
        severity: 'error'
      });
    }
  };

  useEffect(() => {
    let mounted = true;

    const fetchTransactions = async () => {
      try {
        const response = await api.get<Transaction[]>('/transactions/');
        if (mounted) {
          setTransactions(response.data);
        }
      } catch (error) {
        if (mounted) {
          console.error('Erro ao carregar transações:', error);
          setFeedback({
            open: true,
            message: 'Erro ao carregar transações. Tente novamente.',
            severity: 'error'
          });
        }
      }
    };

    fetchTransactions();

    // Verificar se o usuário é admin
    const checkAdminStatus = async () => {
      try {
        const response = await api.get('/auth/users/me');
        if (mounted && response.data) {
          setIsAdmin(response.data.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de admin:', error);
        if (mounted) {
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();

    // Listen for refreshTransactions event from DocumentProcessor
    // Este evento é disparado quando o DocumentProcessor salva as transações
    // Não precisamos salvar as transações novamente, apenas atualizar a lista
    const handleRefreshTransactions = () => {
      console.log('Recebido evento refreshTransactions - atualizando lista de transações');
      fetchTransactions();
      setFeedback({
        open: true,
        message: 'Documentos processados com sucesso!',
        severity: 'success'
      });
    };

    window.addEventListener('refreshTransactions', handleRefreshTransactions);

    return () => {
      mounted = false;
      window.removeEventListener('refreshTransactions', handleRefreshTransactions);
    };
  }, []);

  const loadTransactions = async () => {
    try {
      const response = await api.get<Transaction[]>('/transactions/');
      const validatedTransactions = response.data.map(transaction => ({
        ...transaction,
        type: transaction.type as 'income' | 'expense'
      }));
      setTransactions(validatedTransactions);
    } catch (error) {
      console.error('Erro ao carregar transações:', error);
      setFeedback({
        open: true,
        message: 'Erro ao carregar transações. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleCreateTransaction = async (transactionData: TransactionFormData) => {
    try {
      const formattedData = {
        ...transactionData,
        date: transactionData.date.toISOString(),
        type: transactionData.type.toLowerCase(),
        amount: Number(transactionData.amount),
        subtype: transactionData.subtype
      };

      console.log('Criando transação:', formattedData);

      const response = await api.post('/transactions/', formattedData);
      console.log('Resposta da criação:', response.data);

      await loadTransactions();
      setFeedback({
        open: true,
        message: 'Transação criada com sucesso!',
        severity: 'success'
      });
      setIsTransactionFormOpen(false);
    } catch (error) {
      console.error('Erro ao criar transação:', error);
      setFeedback({
        open: true,
        message: 'Erro ao criar transação. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Este método não é mais usado, pois o DocumentProcessor agora salva as transações diretamente
  // e dispara um evento refreshTransactions para atualizar a lista
  const handleTransactionsExtracted = async (extractedTransactions: Transaction[]) => {
    console.log('handleTransactionsExtracted chamado, mas não faz nada:', extractedTransactions.length);
    // Não fazemos nada aqui para evitar duplicação de transações
  };

  const handleDeleteTransaction = async (id: number) => {
    try {
      await api.delete(`/transactions/${id}`);
      await loadTransactions();
      setFeedback({
        open: true,
        message: 'Transação excluída com sucesso!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erro ao excluir transação:', error);
      setFeedback({
        open: true,
        message: 'Erro ao excluir transação. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleDeleteClick = (id: number) => {
    setDeleteDialog({
      open: true,
      transactionId: id
    });
  };

  const handleConfirmDelete = async () => {
    if (deleteDialog.transactionId) {
      await handleDeleteTransaction(deleteDialog.transactionId);
    }
    setDeleteDialog({
      open: false,
      transactionId: null
    });
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      transactionId: null
    });
  };

  // Função reformulada para lidar corretamente com o clique no botão de editar
  const handleEditClick = (transaction: Transaction) => {
    try {
      console.log('Preparando transação para edição:', transaction);

      // Fechar o formulário se estiver aberto
      if (isTransactionFormOpen) {
        setIsTransactionFormOpen(false);
        setEditingTransaction(undefined);
      }

      // Usando um pequeno atraso para garantir que o estado foi atualizado antes de abrir o formulário
      setTimeout(() => {
        // Clonar a transação para evitar problemas de referência
        const transactionToEdit = JSON.parse(JSON.stringify(transaction));
        console.log('Abrindo editor com transação:', transactionToEdit);
        setEditingTransaction(transactionToEdit);
        setIsTransactionFormOpen(true);
      }, 100);

    } catch (error) {
      console.error('Erro ao preparar edição de transação:', error);
      setFeedback({
        open: true,
        message: 'Erro ao preparar edição. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleEditTransaction = async (transactionData: TransactionFormData) => {
    try {
      if (!editingTransaction?.id) {
        console.error('ID da transação não encontrado');
        return;
      }

      const formattedData = {
        description: transactionData.description,
        amount: Number(transactionData.amount),
        type: transactionData.type,
        category: transactionData.category,
        date: transactionData.date.toISOString(),
        subtype: transactionData.subtype
      };

      console.log(`Editando transação ${editingTransaction.id}:`, formattedData);

      const response = await api.put(`/transactions/${editingTransaction.id}`, formattedData);

      if (response.data) {
        await loadTransactions();
        setFeedback({
          open: true,
          message: 'Transação atualizada com sucesso!',
          severity: 'success'
        });
        setIsTransactionFormOpen(false);
        setEditingTransaction(undefined);
      }
    } catch (error: any) {
      console.error('Erro ao atualizar transação:', error);
      setFeedback({
        open: true,
        message: error.response?.data?.detail || 'Erro ao atualizar transação',
        severity: 'error'
      });
    }
  };

  const handleFilterChange = (filterName: keyof TransactionFiltersProps['filters'], value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const handleCloseFeedback = () => {
    setFeedback(prev => ({
      ...prev,
      open: false
    }));
  };

  const handleLogout = () => {
    signOut();
    navigate('/login');
  };

  const getFilteredTransactions = () => {
    return transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);

      if (filters.startDate) {
        const startDate = new Date(filters.startDate);
        startDate.setHours(0, 0, 0, 0);
        transactionDate.setHours(0, 0, 0, 0);

        if (transactionDate < startDate) return false;
      }

      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        endDate.setHours(23, 59, 59, 999);
        if (transactionDate > endDate) return false;
      }

      const matchesType = !filters.type || transaction.type === filters.type;
      const matchesCategory = !filters.category ||
        transaction.category.toLowerCase().includes(filters.category.toLowerCase());

      return matchesType && matchesCategory;
    });
  };

  const filteredTransactions = getFilteredTransactions();

  // Calcular o saldo total considerando o tratamento especial para cartão de crédito
  const totalBalance = filteredTransactions.reduce((acc, curr) => {
    // Compras no cartão não afetam o saldo diretamente (já são contabilizadas no pagamento da fatura)
    if (curr.type === 'expense' && curr.subtype === 'credit_card_purchase') {
      return acc; // Não afeta o saldo
    }
    // Estornos de cartão classificados como despesa não afetam o saldo diretamente
    else if (curr.type === 'expense' && curr.subtype === 'refund') {
      return acc; // Não afeta o saldo, apenas reduz o crédito
    }
    // Pagamentos de cartão são saídas
    else if (curr.type === 'expense' && curr.subtype === 'credit_card_payment') {
      return acc - curr.amount;
    }
    // Demais transações seguem a regra normal
    else {
      return acc + (curr.type === 'income' ? curr.amount : -curr.amount);
    }
  }, 0);

  // Calcular entradas (apenas receitas reais, excluindo estornos de cartão classificados como despesa)
  const totalIncome = filteredTransactions
    .filter(t => t.type === 'income')
    .reduce((acc, curr) => acc + curr.amount, 0);

  // Calcular saídas excluindo compras no cartão e reembolsos de cartão
  const totalExpenses = filteredTransactions
    .filter(t =>
      t.type === 'expense' &&
      t.subtype !== 'credit_card_purchase' &&
      t.subtype !== 'refund' // Excluir todos os reembolsos
    )
    .reduce((acc, curr) => acc + curr.amount, 0);

  // Calcular o total de crédito (compras no cartão menos reembolsos)
  const totalCredit = filteredTransactions
    .filter(t =>
      // Incluir compras no cartão
      (t.type === 'expense' && t.subtype === 'credit_card_purchase') ||
      // Incluir reembolsos de cartão (com valor negativo para subtrair)
      (t.type === 'expense' && t.subtype === 'refund')
    )
    .reduce((acc, curr) => {
      // Compras no cartão somam ao total
      if (curr.subtype === 'credit_card_purchase') {
        return acc + curr.amount;
      }
      // Reembolsos subtraem do total
      else if (curr.subtype === 'refund') {
        return acc - curr.amount;
      }
      return acc;
    }, 0);

  // Calcular o total de investimentos
  const totalInvestments = filteredTransactions
    .filter(t => t.type === 'investment' || (t.type === 'expense' && t.subtype === 'investment_deposit'))
    .reduce((acc, curr) => acc + curr.amount, 0);

  const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, icon, color }) => (
    <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        {icon}
        <Typography component="h2" variant="h6" color={color} sx={{ ml: 1 }}>
          {title}
        </Typography>
      </Box>
      <Typography component="p" variant="h4" sx={{ color }}>
        {new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(value)}
      </Typography>
    </Paper>
  );

  const groupTransactionsByMonth = (transactions: Transaction[]) => {
    return transactions.reduce((groups, transaction) => {
      const date = new Date(transaction.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthName = date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });

      if (!groups[monthKey]) {
        groups[monthKey] = {
          monthName: monthName.charAt(0).toUpperCase() + monthName.slice(1),
          transactions: []
        };
      }

      groups[monthKey].transactions.push(transaction);
      return groups;
    }, {} as Record<string, { monthName: string; transactions: Transaction[] }>);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppBar com largura 100% */}
      <AppBar position="static" sx={{ width: '100%' }}>
        <Toolbar>
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Finance App
          </Typography>
          {isAdmin && (
            <Button
              color="inherit"
              onClick={() => navigate('/users')}
              startIcon={<PeopleIcon />}
              sx={{ mr: 2 }}
            >
              Usuários
            </Button>
          )}
          <Button color="inherit" onClick={handleLogout} startIcon={<LogoutIcon />}>
            Sair
          </Button>
        </Toolbar>
      </AppBar>

      {/* Container centralizado com 80% de largura para todo o conteúdo */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Paper sx={{ mb: 4 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            centered
          >
            <Tab label="Visão Geral" />
            <Tab label="Análise" />
            <Tab label="Planejamento" />
            <Tab label="Documentos" />
          </Tabs>
        </Paper>

        <TabPanel value={currentTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper sx={{ p: 4, mb: 3 }}>
                <TransactionFilters
                  filters={filters}
                  onFilterChange={handleFilterChange}
                />
              </Paper>
            </Grid>

            <Grid item xs={12} md={2.4}>
              <SummaryCard
                title="Saldo Total"
                value={totalBalance}
                icon={<BalanceIcon sx={{ color: 'primary.main' }} />}
                color="primary.main"
              />
            </Grid>
            <Grid item xs={12} md={2.4}>
              <SummaryCard
                title="Entradas"
                value={totalIncome}
                icon={<IncomeIcon sx={{ color: 'success.main' }} />}
                color="success.main"
              />
            </Grid>
            <Grid item xs={12} md={2.4}>
              <SummaryCard
                title="Saídas"
                value={totalExpenses}
                icon={<ExpenseIcon sx={{ color: 'error.main' }} />}
                color="error.main"
              />
            </Grid>
            <Grid item xs={12} md={2.4}>
              <SummaryCard
                title="Crédito"
                value={totalCredit}
                icon={<CreditCardIcon sx={{ color: 'warning.main' }} />}
                color="warning.main"
              />
            </Grid>
            <Grid item xs={12} md={2.4}>
              <SummaryCard
                title="Investimentos"
                value={totalInvestments}
                icon={<InvestmentIcon sx={{ color: 'info.main' }} />}
                color="info.main"
              />
            </Grid>

            <Grid item xs={12}>
              <Paper sx={{ p: 4 }}>
                <Typography component="h2" variant="h6" color="primary" gutterBottom>
                  Últimas Transações
                </Typography>
                {filteredTransactions.length === 0 ? (
                  <Typography variant="body1" sx={{ textAlign: 'center', py: 3 }}>
                    Nenhuma transação encontrada
                  </Typography>
                ) : (
                  <>
                    {selectedTransactions.length > 0 && (
                      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Typography>
                          {selectedTransactions.length} {selectedTransactions.length === 1 ? 'transação selecionada' : 'transações selecionadas'}
                        </Typography>
                        <Button
                          variant="contained"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={handleBulkDelete}
                        >
                          Excluir Selecionadas
                        </Button>
                      </Box>
                    )}
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedTransactions.length === filteredTransactions.length}
                              indeterminate={selectedTransactions.length > 0 && selectedTransactions.length < filteredTransactions.length}
                              onChange={handleSelectAllVisible}
                            />
                          </TableCell>
                          <TableCell>Data</TableCell>
                          <TableCell>Descrição</TableCell>
                          <TableCell>Tipo</TableCell>
                          <TableCell>Categoria</TableCell>
                          <TableCell>Subtipo</TableCell>
                          <TableCell>Valor</TableCell>
                          <TableCell align="center">Ações</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {Object.entries(groupTransactionsByMonth(filteredTransactions))
                          .sort(([a], [b]) => b.localeCompare(a)) // Ordena meses decrescente
                          .map(([monthKey, { monthName, transactions }]) => (
                            <React.Fragment key={monthKey}>
                              {/* Linha do cabeçalho do mês */}
                              <TableRow>
                                <TableCell
                                  colSpan={8}
                                  sx={{
                                    bgcolor: 'grey.100',
                                    py: 2,
                                    borderBottom: '2px solid',
                                    borderColor: 'primary.main',
                                    typography: 'subtitle1',
                                    fontWeight: 'bold'
                                  }}
                                >
                                  {monthName}
                                </TableCell>
                              </TableRow>

                              {/* Espaçamento após o cabeçalho do mês */}
                              <TableRow>
                                <TableCell colSpan={8} sx={{ py: 1, border: 'none' }} />
                              </TableRow>

                              {/* Transações do mês */}
                              {transactions.map((transaction) => (
                                <TableRow
                                  key={transaction.id}
                                  selected={selectedTransactions.includes(transaction.id)}
                                >
                                  <TableCell padding="checkbox">
                                    <Checkbox
                                      checked={selectedTransactions.includes(transaction.id)}
                                      onChange={() => handleSelectTransaction(transaction.id)}
                                    />
                                  </TableCell>
                                  <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                                  <TableCell>
                                    {transaction.type === 'expense' && transaction.subtype === 'refund' ?
                                      <>
                                        <span style={{ fontWeight: 'bold', color: '#B45309' }}>Estorno:</span> {transaction.description}
                                      </> :
                                      transaction.description
                                    }
                                  </TableCell>
                                  <TableCell>
                                    <Box
                                      sx={{
                                        display: 'inline-block',
                                        bgcolor: transaction.type === 'income' ? 'success.main' :
                                                transaction.type === 'expense' ? 'error.main' :
                                                transaction.type === 'investment' ? 'info.main' : 'primary.main',
                                        color: 'white',
                                        px: 1,
                                        py: 0.5,
                                        borderRadius: 1,
                                        fontSize: '0.75rem',
                                        fontWeight: 'bold'
                                      }}
                                    >
                                      {transaction.type === 'income' ? 'Receita' :
                                       (transaction.type === 'expense' && (transaction.subtype === 'refund' || transaction.is_refund)) ?
                                       <span style={{ textDecoration: 'line-through' }}>Despesa</span> :
                                       transaction.type === 'expense' ? 'Despesa' : 'Investimento'}
                                    </Box>
                                  </TableCell>
                                  <TableCell>{transaction.category.charAt(0).toUpperCase() + transaction.category.slice(1)}</TableCell>
                                  <TableCell>
                                    <Box
                                      sx={{
                                        display: 'inline-block',
                                        bgcolor: transaction.subtype === 'credit_card_purchase' ? 'warning.main' :
                                                 (transaction.subtype === 'refund' && transaction.type === 'income') ? 'success.main' :
                                                 ((transaction.subtype === 'refund' && transaction.type === 'expense') || transaction.is_refund) ? '#B45309' : // Laranja mais escuro para estornos
                                                 transaction.subtype === 'credit_card_payment' ? 'error.main' :
                                                 transaction.subtype === 'investment_deposit' || transaction.subtype === 'investment_withdrawal' ? 'info.main' :
                                                 'grey.500',
                                        color: 'white',
                                        px: 1,
                                        py: 0.5,
                                        borderRadius: 1,
                                        fontSize: '0.75rem',
                                        ...((transaction.subtype === 'refund' && transaction.type === 'expense') || transaction.is_refund) && {
                                          backgroundImage: 'repeating-linear-gradient(45deg, #B45309, #B45309 10px, #4B5563 10px, #4B5563 20px)',
                                          fontWeight: 'bold',
                                        }
                                      }}
                                    >
                                      {SUBTYPE_LABELS[transaction.subtype] || transaction.subtype}
                                    </Box>
                                  </TableCell>
                                  <TableCell
                                    align="center"
                                    sx={{
                                      color: 'white',
                                      bgcolor: transaction.type === 'income' ? 'success.main' :
                                             (transaction.type === 'expense' && transaction.subtype === 'credit_card_purchase') ? 'warning.main' :
                                             ((transaction.type === 'expense' && transaction.subtype === 'refund') || transaction.is_refund) ? '#B45309' : // Laranja mais escuro para estornos de cartão
                                             transaction.type === 'investment' ? 'info.main' : 'error.main',
                                      borderRadius: 1,
                                      fontWeight: 'bold',
                                      width: '100px',
                                      padding: '6px 8px',
                                      ...((transaction.type === 'expense' && transaction.subtype === 'refund') || transaction.is_refund) && {
                                        backgroundImage: 'repeating-linear-gradient(45deg, #B45309, #B45309 10px, #4B5563 10px, #4B5563 20px)',
                                      }
                                    }}
                                  >
                                    {new Intl.NumberFormat('pt-BR', {
                                      style: 'currency',
                                      currency: 'BRL'
                                    }).format(transaction.amount)}
                                  </TableCell>
                                  <TableCell align="center">
                                    <Tooltip title="Editar">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleEditClick(transaction)}
                                      >
                                        <EditIcon />
                                      </IconButton>
                                    </Tooltip>
                                    <Tooltip title="Excluir">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleDeleteClick(transaction.id)}
                                        color="error"
                                      >
                                        <DeleteIcon />
                                      </IconButton>
                                    </Tooltip>
                                  </TableCell>
                                </TableRow>
                              ))}

                              {/* Espaçamento após as transações do mês */}
                              <TableRow>
                                <TableCell colSpan={8} sx={{ py: 1, border: 'none' }} />
                              </TableRow>
                            </React.Fragment>
                          ))}
                      </TableBody>
                    </Table>
                  </>
                )}
              </Paper>
            </Grid>

            {/* AI Insights Component */}
            <Grid item xs={12}>
              <AIInsights
                filters={filters}
                onFeedback={(message, severity) =>
                  setFeedback({
                    open: true,
                    message,
                    severity
                  })
                }
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <FinancialAnalytics transactions={transactions} />
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <AISavingsRecommendations
                onFeedback={(message, severity) =>
                  setFeedback({
                    open: true,
                    message,
                    severity
                  })
                }
              />
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 4, mb: 3 }}>
                <FinancialGoalList
                  onFeedback={(message, severity) =>
                    setFeedback({
                      open: true,
                      message,
                      severity
                    })
                  }
                />
              </Paper>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 4 }}>
                <BudgetManager
                  onFeedback={(message, severity) =>
                    setFeedback({
                      open: true,
                      message,
                      severity
                    })
                  }
                />
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={3}>
          {/* Ferramenta de diagnóstico de autenticação */}
          <TestUploadAuth />

          {/* Envolve o DocumentProcessor em um Paper/Container consistente com as outras abas */}
          <Paper sx={{ p: 4 }}>
            <DocumentProcessor onTransactionsExtracted={handleTransactionsExtracted} />
          </Paper>
        </TabPanel>

        <Fab
          color="primary"
          aria-label="add transaction"
          onClick={() => setIsTransactionFormOpen(true)}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
          }}
        >
          <AddIcon />
        </Fab>

        <TransactionForm
          open={isTransactionFormOpen}
          onClose={() => {
            setIsTransactionFormOpen(false);
            setEditingTransaction(undefined);
          }}
          onSubmit={editingTransaction ? handleEditTransaction : handleCreateTransaction}
          transaction={editingTransaction}
        />

        <DeleteTransactionDialog
          open={deleteDialog.open}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleConfirmDelete}
        />

        <Snackbar
          open={feedback.open}
          autoHideDuration={6000}
          onClose={handleCloseFeedback}
        >
          <Alert
            onClose={handleCloseFeedback}
            severity={feedback.severity}
            variant="filled"
          >
            {feedback.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Dashboard;