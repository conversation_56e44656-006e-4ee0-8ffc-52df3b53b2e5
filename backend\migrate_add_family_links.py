#!/usr/bin/env python3
"""
Migração para criar a tabela family_links
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_family_links():
    """Cria a tabela family_links"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # Verificar se a tabela já existe
            result = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='family_links'"))
            table_exists = result.fetchone() is not None
            
            if table_exists:
                logger.info("✅ Tabela family_links já existe")
                return True
            
            # Criar a tabela family_links
            logger.info("Criando tabela family_links...")
            connection.execute(text("""
                CREATE TABLE family_links (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    requester_id INTEGER NOT NULL,
                    invited_id INTEGER NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    created_at DATETIME DEFAULT (datetime('now')),
                    updated_at DATETIME DEFAULT (datetime('now')),
                    FOREIGN KEY (requester_id) REFERENCES users (id),
                    FOREIGN KEY (invited_id) REFERENCES users (id),
                    UNIQUE(requester_id, invited_id)
                )
            """))
            connection.commit()
            
            logger.info("✅ Migração concluída com sucesso!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro na migração: {str(e)}")
        return False

if __name__ == "__main__":
    success = migrate_add_family_links()
    if success:
        print("✅ Migração executada com sucesso!")
        sys.exit(0)
    else:
        print("❌ Falha na migração!")
        sys.exit(1)
