# Configuração do Google OAuth2

Este documento explica como configurar o Google OAuth2 no Finance App.

## Pré-requisitos

1. Conta do Google
2. Projeto no Google Cloud Console
3. Credenciais OAuth2 configuradas

## Passo a Passo

### 1. Criar Projeto no Google Cloud Console

1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou selecione um existente
3. Ative a API "Google+ API" ou "Google Identity"

### 2. Configurar OAuth2

1. Vá para "APIs & Services" > "Credentials"
2. Clique em "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure:
   - Application type: Web application
   - Name: Finance App
   - Authorized JavaScript origins: `http://localhost:3000`
   - Authorized redirect URIs: `http://localhost:8000/api/v1/auth/google/callback`

### 3. Configurar Variáveis de Ambiente

Copie o arquivo `.env.example` para `.env` e configure:

```bash
cp backend/.env.example backend/.env
```

Edite o arquivo `.env` com suas credenciais:

```env
GOOGLE_CLIENT_ID=seu-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=seu-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

### 4. Instalar Dependências

```bash
cd backend
pip install google-auth google-auth-oauthlib google-auth-httplib2
```

### 5. Testar a Configuração

1. Inicie o backend:
```bash
cd backend
python -m uvicorn app.main:app --reload
```

2. Verifique o status do OAuth2:
```bash
curl http://localhost:8000/api/v1/auth/google/status
```

3. Abra o arquivo de demo:
```bash
# Abra frontend/google-oauth-demo.html no navegador
```

## Endpoints Disponíveis

### GET /api/v1/auth/google/status
Verifica se o Google OAuth2 está configurado.

**Resposta:**
```json
{
  "enabled": true,
  "message": "Google OAuth2 configurado"
}
```

### GET /api/v1/auth/google/login
Retorna a URL de autorização do Google.

**Resposta:**
```json
{
  "authorization_url": "https://accounts.google.com/o/oauth2/auth?..."
}
```

### POST /api/v1/auth/google/token
Autentica o usuário com token do Google.

**Corpo da requisição:**
```json
{
  "token": "eyJhbGciOiJSUzI1NiIs..."
}
```

**Resposta:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "<EMAIL>",
    "full_name": "Nome do Usuário",
    "profile_picture": "https://lh3.googleusercontent.com/...",
    "is_verified": true
  }
}
```

## Integração Frontend

### React/JavaScript

```javascript
// Inicializar Google Sign-In
google.accounts.id.initialize({
  client_id: 'SEU_GOOGLE_CLIENT_ID',
  callback: handleCredentialResponse
});

// Callback de autenticação
async function handleCredentialResponse(response) {
  const authResponse = await fetch('/api/v1/auth/google/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token: response.credential })
  });
  
  const data = await authResponse.json();
  // Usar data.access_token para autenticação
}
```

## Segurança

- O token do Google é verificado no backend
- Usuários são criados automaticamente se não existirem
- Contas do Google são consideradas verificadas
- Tokens de acesso seguem o padrão JWT do sistema

## Troubleshooting

### Erro: "Google OAuth2 não está configurado"
- Verifique se `GOOGLE_CLIENT_ID` e `GOOGLE_CLIENT_SECRET` estão definidos no `.env`
- Reinicie o servidor backend

### Erro: "Token do Google inválido"
- Verifique se o `GOOGLE_CLIENT_ID` está correto
- Certifique-se de que o domínio está autorizado no Google Cloud Console

### Erro: "Erro ao criar conta"
- Verifique se o banco de dados está acessível
- Verifique se as migrações foram executadas
