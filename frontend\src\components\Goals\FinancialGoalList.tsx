import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  LinearProgress,
  Chip,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Card,
  CardContent,
  CardActions,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Payments as PaymentsIcon,
} from '@mui/icons-material';
import api from '../../services/api';
import FinancialGoalForm from './FinancialGoalForm';
import { ContributeToGoalDialog } from './index';

interface FinancialGoal {
  id: number;
  name: string;
  description: string;
  target_amount: number;
  current_amount: number;
  target_date: string | null;
  status: 'active' | 'completed' | 'canceled';
  created_at: string;
}

interface FinancialGoalFormData {
  name: string;
  description: string;
  target_amount: number;
  current_amount: number;
  target_date: Date | null;
  status: 'active' | 'completed' | 'canceled';
}

interface FinancialGoalListProps {
  onFeedback?: (message: string, severity: 'success' | 'error') => void;
}

const FinancialGoalList: React.FC<FinancialGoalListProps> = ({ onFeedback }) => {
  const [goals, setGoals] = useState<FinancialGoal[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contributeDialogOpen, setContributeDialogOpen] = useState(false);
  const [currentGoal, setCurrentGoal] = useState<FinancialGoal | null>(null);

  useEffect(() => {
    loadGoals();
  }, []);

  const loadGoals = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/financial-goals/');
      setGoals(response.data);
    } catch (err) {
      console.error('Erro ao carregar metas financeiras:', err);
      setError('Erro ao carregar metas financeiras. Tente novamente.');
      if (onFeedback) {
        onFeedback('Erro ao carregar metas financeiras', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGoal = async (data: FinancialGoalFormData) => {
    try {
      await api.post('/financial-goals/', {
        ...data,
        target_date: data.target_date ? data.target_date.toISOString() : null,
      });

      await loadGoals();

      if (onFeedback) {
        onFeedback('Meta financeira criada com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao criar meta financeira:', err);
      if (onFeedback) {
        onFeedback('Erro ao criar meta financeira', 'error');
      }
      throw err;
    }
  };

  const handleUpdateGoal = async (data: FinancialGoalFormData) => {
    if (!currentGoal) return;

    try {
      await api.put(`/financial-goals/${currentGoal.id}/`, {
        ...data,
        target_date: data.target_date ? data.target_date.toISOString() : null,
      });

      await loadGoals();

      if (onFeedback) {
        onFeedback('Meta financeira atualizada com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao atualizar meta financeira:', err);
      if (onFeedback) {
        onFeedback('Erro ao atualizar meta financeira', 'error');
      }
      throw err;
    }
  };

  const handleDeleteGoal = async () => {
    if (!currentGoal) return;

    try {
      await api.delete(`/financial-goals/${currentGoal.id}/`);
      await loadGoals();
      setDeleteDialogOpen(false);
      setCurrentGoal(null);

      if (onFeedback) {
        onFeedback('Meta financeira excluída com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao excluir meta financeira:', err);
      if (onFeedback) {
        onFeedback('Erro ao excluir meta financeira', 'error');
      }
    }
  };

  const handleContributeToGoal = async (amount: number, transaction_id?: number) => {
    if (!currentGoal) return;

    try {
      await api.post(`/financial-goals/${currentGoal.id}/contribute/`, {
        amount,
        transaction_id
      });

      await loadGoals();
      setContributeDialogOpen(false);

      if (onFeedback) {
        onFeedback('Contribuição adicionada com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao adicionar contribuição:', err);
      if (onFeedback) {
        onFeedback('Erro ao adicionar contribuição', 'error');
      }
      throw err;
    }
  };

  const handleOpenForm = (goal?: FinancialGoal) => {
    setCurrentGoal(goal || null);
    setFormOpen(true);
  };

  const handleOpenDeleteDialog = (goal: FinancialGoal) => {
    setCurrentGoal(goal);
    setDeleteDialogOpen(true);
  };

  const handleOpenContributeDialog = (goal: FinancialGoal) => {
    setCurrentGoal(goal);
    setContributeDialogOpen(true);
  };

  const calculateProgress = (current: number, target: number): number => {
    if (target <= 0) return 0;
    const progress = (current / target) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'primary';
      case 'completed':
        return 'success';
      case 'canceled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Em andamento';
      case 'completed':
        return 'Concluída';
      case 'canceled':
        return 'Cancelada';
      default:
        return status;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Função para calcular os dias restantes até a data alvo
  const getRemainingDays = (targetDate: string): number | null => {
    if (!targetDate) return null;

    const target = new Date(targetDate);
    const now = new Date();

    const diffTime = target.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  };

  const renderRemainingTime = (goal: FinancialGoal) => {
    if (!goal.target_date) return null;

    const remainingDays = getRemainingDays(goal.target_date);
    if (remainingDays === null) return null;

    if (remainingDays === 0) {
      return <Typography variant="caption" color="error">Prazo vencido</Typography>;
    }

    return (
      <Typography variant="caption">
        {remainingDays} {remainingDays === 1 ? 'dia restante' : 'dias restantes'}
      </Typography>
    );
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Metas Financeiras</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenForm()}
        >
          Nova Meta
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {goals.length === 0 && !loading && !error ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            Você ainda não tem metas financeiras. Crie sua primeira meta para começar a planejar seu futuro financeiro.
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
            sx={{ mt: 2 }}
          >
            Criar Primeira Meta
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {goals.map((goal) => (
            <Grid item xs={12} md={6} lg={4} key={goal.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    <Typography variant="h6" gutterBottom>
                      {goal.name}
                    </Typography>
                    <Chip
                      size="small"
                      label={getStatusLabel(goal.status)}
                      color={getStatusColor(goal.status) as any}
                    />
                  </Box>

                  {goal.description && (
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {goal.description}
                    </Typography>
                  )}

                  <Box sx={{ mt: 2 }}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Progresso:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {calculateProgress(goal.current_amount, goal.target_amount).toFixed(1)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={calculateProgress(goal.current_amount, goal.target_amount)}
                      sx={{ my: 1, height: 8, borderRadius: 1 }}
                    />
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">{formatCurrency(goal.current_amount)}</Typography>
                      <Typography variant="body2">{formatCurrency(goal.target_amount)}</Typography>
                    </Box>
                  </Box>

                  {goal.target_date && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        Data alvo: {new Date(goal.target_date).toLocaleDateString()}
                      </Typography>
                      {renderRemainingTime(goal)}
                    </Box>
                  )}
                </CardContent>
                <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                  <Box>
                    {goal.status === 'active' && (
                      <Tooltip title="Adicionar Contribuição">
                        <IconButton
                          onClick={() => handleOpenContributeDialog(goal)}
                          color="primary"
                        >
                          <PaymentsIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Editar">
                      <IconButton onClick={() => handleOpenForm(goal)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                  <Tooltip title="Excluir">
                    <IconButton
                      onClick={() => handleOpenDeleteDialog(goal)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Form Dialog */}
      <FinancialGoalForm
        open={formOpen}
        onClose={() => {
          setFormOpen(false);
          setCurrentGoal(null);
        }}
        onSubmit={currentGoal ? handleUpdateGoal : handleCreateGoal}
        goal={currentGoal || undefined}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Tem certeza que deseja excluir a meta financeira "{currentGoal?.name}"? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="primary">
            Cancelar
          </Button>
          <Button onClick={handleDeleteGoal} color="error" variant="contained">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Contribute Dialog */}
      {currentGoal && (
        <ContributeToGoalDialog
          open={contributeDialogOpen}
          onClose={() => setContributeDialogOpen(false)}
          onSubmit={handleContributeToGoal}
          goal={currentGoal}
        />
      )}
    </Box>
  );
};

export default FinancialGoalList;