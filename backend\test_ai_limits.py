#!/usr/bin/env python3
"""
Script para testar o endpoint de limites de IA
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_assistance import finance_ai
from app.database import get_db, SessionLocal
from app.models.financial import User, Transaction
from datetime import datetime, timedelta
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ai_limits():
    """Testa o endpoint de limites de IA"""
    
    # Criar sessão do banco
    db = SessionLocal()
    
    try:
        # Buscar usuário admin
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not user:
            logger.error("Usuário <EMAIL> não encontrado")
            return
        
        logger.info(f"Usuário encontrado: {user.email} (ID: {user.id})")
        
        # Verificar se há transações
        transactions = db.query(Transaction).filter(Transaction.user_id == user.id).all()
        logger.info(f"Transações encontradas: {len(transactions)}")
        
        # Se não há transações, criar algumas de teste
        if len(transactions) < 5:
            logger.info("Criando transações de teste...")
            test_transactions = [
                Transaction(
                    user_id=user.id,
                    description="Mercado",
                    amount=150.00,
                    type="expense",
                    category="alimentação",
                    date=datetime.now() - timedelta(days=10)
                ),
                Transaction(
                    user_id=user.id,
                    description="Restaurante",
                    amount=80.00,
                    type="expense",
                    category="alimentação",
                    date=datetime.now() - timedelta(days=15)
                ),
                Transaction(
                    user_id=user.id,
                    description="Gasolina",
                    amount=120.00,
                    type="expense",
                    category="transporte",
                    date=datetime.now() - timedelta(days=5)
                ),
                Transaction(
                    user_id=user.id,
                    description="Cinema",
                    amount=40.00,
                    type="expense",
                    category="lazer",
                    date=datetime.now() - timedelta(days=20)
                ),
                Transaction(
                    user_id=user.id,
                    description="Salário",
                    amount=3000.00,
                    type="income",
                    category="salário",
                    date=datetime.now() - timedelta(days=30)
                )
            ]
            
            for transaction in test_transactions:
                db.add(transaction)
            db.commit()
            logger.info("Transações de teste criadas")
        
        # Testar o serviço de IA
        logger.info("Testando serviço de IA...")
        
        # Verificar se está disponível
        is_available = await finance_ai.is_available()
        logger.info(f"IA disponível: {is_available}")
        
        # Testar sugestão de limites
        logger.info("Testando sugestão de limites...")
        result = await finance_ai.suggest_spending_limits(user.id, db)
        
        logger.info("Resultado:")
        logger.info(f"Mensagem: {result.get('message')}")
        logger.info(f"Limites: {result.get('limits')}")
        logger.info(f"Estatísticas: {result.get('spending_statistics')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Erro no teste: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None
    finally:
        db.close()

if __name__ == "__main__":
    result = asyncio.run(test_ai_limits())
    if result:
        print("\n=== TESTE CONCLUÍDO COM SUCESSO ===")
        print(f"Mensagem: {result.get('message')}")
        print(f"Número de limites: {len(result.get('limits', []))}")
    else:
        print("\n=== TESTE FALHOU ===")
