#!/usr/bin/env python3

import requests
import json

def test_ai_limits():
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 TESTING AI SPENDING LIMITS")
    print("=" * 50)
    
    try:
        # Test 1: Login to get token
        print("1. Testing login...")
        login_data = {
            'username': '<EMAIL>',
            'password': 'admin123'
        }
        
        response = requests.post(f"{base_url}/auth/token", data=login_data, timeout=10)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return
        
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print("✅ Login successful")
        
        # Test 2: Check AI status
        print("\n2. Testing AI status...")
        response = requests.get(f"{base_url}/ai/status", headers=headers, timeout=10)
        print(f"   AI Status: {response.status_code}")
        if response.status_code == 200:
            status_data = response.json()
            print(f"   AI Enabled: {status_data.get('enabled', False)}")
            print(f"   AI Available: {status_data.get('available', False)}")
        
        # Test 3: Test spending limits endpoint
        print("\n3. Testing spending limits...")
        response = requests.get(f"{base_url}/ai/spending-limits", headers=headers, timeout=30)
        print(f"   Spending limits: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ AI Spending Limits working!")
            data = response.json()
            print(f"   Message: {data.get('message', 'No message')}")
            limits = data.get('limits', [])
            print(f"   Number of limits: {len(limits)}")
            if limits:
                print("   Sample limits:")
                for limit in limits[:3]:  # Show first 3
                    print(f"     - {limit.get('category', 'Unknown')}: R$ {limit.get('limit', 0):.2f}")
        else:
            print(f"❌ Spending limits failed: {response.text[:200]}")
        
        print("\n" + "=" * 50)
        print("🎯 AI LIMITS TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")

if __name__ == "__main__":
    test_ai_limits()
