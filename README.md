# Finance App

Sistema de gerenciamento financeiro pessoal com processamento inteligente de documentos e análise por IA.

## Funcionalidades Principais

### 1. Gerenciamento Financeiro
- Controle de receitas e despesas
- Categorização de transações
- Relatórios e análises
- Metas financeiras
- Orçamentos
- Gestão de cartões de crédito
- Controle de investimentos

### 2. Processamento Inteligente de Documentos
- Extração automática de dados de notas fiscais e faturas
- Detecção inteligente do tipo de documento
- Categorização automática de transações
- Suporte para múltiplos formatos (PDF, imagens)
- Cache de debug para diagnóstico

### 3. Análise por Inteligência Artificial
- Insights financeiros personalizados
- Categorização automática de transações
- Recomendações de economia
- Limites de gastos inteligentes
- Planejamento de metas assistido por IA

### 4. Segurança Avançada
- Autenticação com refresh tokens
- Proteção contra ataques CSRF
- Limitação de tentativas de login
- Validação forte de senhas
- Sanitização de dados

## Guia de Funcionalidades

### Gerenciamento de Investimentos
O sistema agora diferencia adequadamente investimentos de despesas comuns, permitindo o acompanhamento correto do seu patrimônio.

#### Como usar:
1. Acesse a seção "Investimentos" no menu principal
2. Para adicionar um novo investimento, clique em "Novo Investimento"
3. Preencha os dados:
   - Nome do investimento
   - Valor inicial
   - Tipo (poupança, ações, títulos, fundos, criptomoedas, imóveis, etc.)
   - Data de início
   - Taxa de juros (se aplicável)
4. Para atualizar o valor de um investimento:
   - Acesse o investimento específico
   - Clique em "Atualizar Valor" para registro manual
   - O sistema também pode simular atualizações automáticas baseadas no tipo de investimento

#### Benefícios:
- Transações de investimento são registradas separadamente das despesas
- Acompanhamento de rendimentos e perdas
- Visualização consolidada do portfólio de investimentos
- Análise de desempenho por tipo de investimento

### Gestão de Cartões de Crédito
Sistema completo para gerenciamento de cartões de crédito, desde o registro de compras até o pagamento de faturas.

#### Como usar:
1. Cadastre seus cartões na seção "Cartões de Crédito"
2. Para cada cartão, você pode:
   - Registrar compras manualmente ou via importação de faturas
   - Categorizar gastos com subcategorias específicas (farmácia, supermercado, etc.)
   - Visualizar faturas atuais e anteriores
   - Fechar faturas manualmente ou automaticamente nas datas configuradas
   - Registrar pagamentos de faturas (que não contam como despesas duplicadas)

#### Benefícios:
- Evita contagem dupla (na compra e no pagamento da fatura)
- Permite visualização detalhada dos gastos por categoria
- Acompanhamento de datas de fechamento e vencimento
- Histórico completo de faturas e pagamentos

### Metas Financeiras
Defina objetivos financeiros e acompanhe seu progresso rumo a conquistas importantes.

#### Como usar:
1. Na seção "Metas", clique em "Nova Meta"
2. Defina:
   - Nome e descrição da meta (ex: "Comprar apartamento")
   - Valor alvo
   - Data limite para atingir o objetivo
   - Categoria relacionada (opcional)
3. Acompanhe o progresso na visualização de metas
4. Registre contribuições específicas para suas metas
5. Receba análises de IA sobre como alcançar suas metas mais rapidamente

#### Benefícios:
- Visualização clara do progresso de cada objetivo
- Notificações quando metas são atingidas
- Recomendações personalizadas de economia para alcançar seus objetivos
- Integração com sua análise financeira geral

### Orçamentos por Categoria
Estabeleça limites de gastos por categoria para manter suas finanças sob controle.

#### Como usar:
1. Acesse "Orçamentos" no menu principal
2. Clique em "Novo Orçamento"
3. Selecione a categoria (alimentação, transporte, lazer, etc.)
4. Defina o limite mensal
5. Configure o período de validade
6. Monitore o progresso de cada orçamento no painel

#### Benefícios:
- Controle visual de gastos por categoria
- Alertas quando os limites estão próximos ou foram ultrapassados
- Análise de tendências de gastos por categoria
- Recomendações para ajustes baseados no seu histórico

### Dashboard Aprimorado
O novo painel principal oferece uma visão completa e integrada da sua saúde financeira.

#### Informações disponíveis:
- Resumo de contas e saldos
- Visão geral de receitas e despesas
- Status de investimentos e retornos
- Progresso de metas financeiras
- Estado atual dos orçamentos por categoria
- Saldo de cartões de crédito e datas importantes
- Análise de fluxo de caixa
- Previsão de gastos recorrentes

## Requisitos do Sistema

- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- [Ollama](https://ollama.ai) ou [LM Studio](https://lmstudio.ai) (opcional, para recursos de IA)

## Instalação

### Backend

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/finance-app.git
cd finance-app/backend
```

2. Crie e ative um ambiente virtual:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. Instale as dependências:
```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite .env com suas configurações
```

### Frontend

1. Instale as dependências:
```bash
cd ../frontend
npm install
```

## Configuração

### 1. Banco de Dados
```bash
cd backend
alembic upgrade head
```

### 2. Sistema de Logs
Os logs são configurados automaticamente em:
- `/logs/app.log`: Logs gerais
- `/logs/error.log`: Logs de erro
- `/logs/debug.log`: Logs de debug (quando ativado)

### 3. Módulo de IA (Opcional)
Consulte `backend/finance_app.egg-info/AI_MODULE_README.md` para instruções detalhadas.

## Execução

### Backend
```bash
cd backend
uvicorn app.main:app --reload
```

### Frontend
```bash
cd frontend
npm run dev
```

## Documentação da API

Acesse `http://localhost:8000/docs` para documentação interativa da API.

## Segurança

### Configurações Recomendadas
1. Ative HTTPS em produção
2. Configure limites adequados de requisições
3. Mantenha as dependências atualizadas
4. Use senhas fortes para banco de dados e admin
5. Configure corretamente as variáveis de ambiente

### Variáveis de Ambiente Críticas
```env
SECRET_KEY=sua_chave_secreta
JWT_SECRET_KEY=sua_chave_jwt
CSRF_SECRET=sua_chave_csrf
DATABASE_URL=sua_url_banco
```

## Resolução de Problemas

### Logs
- Verifique `/logs/error.log` para erros
- Ative modo debug em `.env` para logs detalhados
- Use `/logs/debug.log` para diagnóstico

### Processamento de Documentos
- Verifique permissões de pasta para uploads
- Confirme suporte ao formato do documento
- Consulte cache de debug se ativado

### Módulo de IA
- Verifique status do Ollama/LM Studio
- Confirme configurações no `.env`
- Consulte logs específicos de IA

## Contribuição

1. Fork o projeto
2. Crie sua branch (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Distribuído sob a licença MIT. Veja `LICENSE` para mais informações.