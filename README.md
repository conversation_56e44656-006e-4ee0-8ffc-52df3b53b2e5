# Finance App

Uma aplicação web completa para gerenciamento financeiro pessoal e familiar, desenvolvida com FastAPI (backend) e React (frontend). O sistema oferece controle financeiro avançado com inteligência artificial, gestão familiar e análises detalhadas.

## 🚀 Funcionalidades Principais

### 💰 Gestão Financeira Avançada
- **Transações Completas**: Criação, edição e exclusão de receitas, despesas e investimentos
- **Sistema de Propriedade**: Cada transação pode ter um dono específico (você ou membros da família)
- **Categorização Inteligente**: IA para classificação automática baseada em descrição e valor
- **Cartão de Crédito**: Gestão específica com controle de compras, pagamentos e estornos
- **Investimentos**: Controle detalhado de aplicações, resgates e rendimentos
- **Filtros Globais**: Sistema de filtros persistentes por data, tipo, categoria e membro da família

### 🎯 Interface e Navegação
- **Navegação Colapsável**: Barra de navegação responsiva que se esconde ao fazer scroll
- **Ícone Flutuante**: Acesso rápido ao menu através de ícone flutuante
- **Páginas Separadas**: Organização em páginas dedicadas (Dashboard, Análise, Planejamento, Documentos, Família)
- **Filtros Persistentes**: Filtros mantidos entre sessões e páginas
- **Design Responsivo**: Interface adaptável para desktop e mobile

### 👨‍👩‍👧‍👦 Gestão Familiar Completa
- **Contas Familiares**: Sistema multi-usuário com compartilhamento de dados
- **Permissões Granulares**: Controle de acesso baseado em roles (admin/usuário)
- **Transações Compartilhadas**: Criação de transações em nome de outros membros
- **Visão Consolidada**: Dashboard unificado das finanças familiares
- **Convites por Email**: Sistema de convites para novos membros

### 🤖 Inteligência Artificial Integrada
- **Categorização Automática**: IA local usando Ollama/Llama3 para classificar transações
- **Insights Inteligentes**: Análises automáticas de padrões de gastos
- **Recomendações Personalizadas**: Sugestões de economia e otimização financeira
- **Limites de Gastos**: IA sugere limites baseados no histórico
- **Aprendizado Contínuo**: Sistema aprende com as correções do usuário

### 📊 Análises e Relatórios Avançados
- **Dashboard Interativo**: Métricas principais com visualizações em tempo real
- **Gráficos Dinâmicos**: Visualizações interativas por categoria, período e tipo
- **Análise Temporal**: Evolução mensal e anual dos gastos
- **Métricas Específicas**: Controle separado para crédito, investimentos e saldo
- **Filtros Avançados**: Análise por múltiplos critérios simultaneamente

### 📄 Processamento de Documentos
- **Upload Inteligente**: Processamento automático de extratos bancários
- **Múltiplos Formatos**: Suporte a CSV, PDF e outros formatos
- **Detecção Automática**: Identificação de colunas e tipos de transação
- **Prevenção de Duplicatas**: Sistema inteligente para evitar transações duplicadas
- **Validação de Dados**: Verificação automática de consistência

### 🎯 Planejamento Financeiro
- **Metas Financeiras**: Definição e acompanhamento de objetivos
- **Orçamentos**: Controle de gastos por categoria
- **Projeções**: Estimativas baseadas no histórico
- **Alertas**: Notificações quando próximo aos limites

## 🛠️ Arquitetura Técnica

### Backend (FastAPI)
```
backend/
├── app/
│   ├── main.py              # Aplicação principal
│   ├── config.py            # Configurações centralizadas
│   ├── models/              # Modelos SQLAlchemy
│   │   ├── financial.py     # Transações, contas, investimentos
│   │   ├── user.py          # Usuários e autenticação
│   │   └── family.py        # Gestão familiar
│   ├── routers/             # Endpoints da API
│   │   ├── auth.py          # Autenticação e OAuth
│   │   ├── financial.py     # Transações e análises
│   │   ├── family.py        # Gestão familiar
│   │   └── ai.py            # Endpoints de IA
│   ├── schemas/             # Validação Pydantic
│   ├── services/            # Lógica de negócio
│   └── utils/               # Utilitários
├── migrations/              # Migrações Alembic
└── requirements.txt         # Dependências Python
```

### Frontend (React + TypeScript)
```
frontend/
├── src/
│   ├── components/          # Componentes reutilizáveis
│   │   ├── GlobalFilters/   # Sistema de filtros globais
│   │   ├── Navigation/      # Navegação colapsável
│   │   ├── Transactions/    # Gestão de transações
│   │   ├── Analytics/       # Gráficos e análises
│   │   ├── Family/          # Gestão familiar
│   │   └── AI/              # Componentes de IA
│   ├── pages/               # Páginas principais
│   │   ├── Dashboard.tsx    # Visão geral
│   │   ├── Analysis.tsx     # Análises detalhadas
│   │   ├── Planning.tsx     # Planejamento financeiro
│   │   ├── Documents.tsx    # Processamento de documentos
│   │   ├── Family.tsx       # Gestão familiar
│   │   └── UserManagement.tsx # Administração
│   ├── contexts/            # Contextos React
│   │   ├── AuthContext.tsx  # Autenticação
│   │   └── FiltersContext.tsx # Filtros globais
│   ├── services/            # Serviços de API
│   └── utils/               # Utilitários
└── package.json             # Dependências Node.js
```

### Banco de Dados
- **SQLite**: Desenvolvimento e produção pequena
- **PostgreSQL**: Produção (configurável)
- **Migrações**: Versionamento com Alembic
- **Relacionamentos**: Foreign keys e constraints

### Inteligência Artificial
- **Ollama**: Servidor local de LLM
- **Llama 3**: Modelo de linguagem principal
- **Scikit-learn**: Algoritmos de ML complementares
- **Processamento Local**: Privacidade garantida

## 📦 Instalação e Configuração

### Pré-requisitos
- Python 3.11+
- Node.js 18+
- Git
- Conda (recomendado)

### 1. Clonagem do Repositório
```bash
git clone https://github.com/trsystems/finance-app.git
cd finance-app
```

### 2. Configuração do Backend
```bash
cd backend

# Criar ambiente virtual
conda create -n finance-app python=3.11
conda activate finance-app

# Instalar dependências
pip install -r requirements.txt

# Configurar banco de dados
alembic upgrade head

# Iniciar servidor
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. Configuração do Frontend
```bash
cd frontend

# Instalar dependências
npm install

# Iniciar aplicação
npm start
```

### 4. Configuração da IA (Opcional)
```bash
# Instalar Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Baixar modelo Llama 3
ollama pull llama3:latest

# Iniciar servidor Ollama
ollama serve
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente
Crie `backend/.env`:
```env
# Aplicação
SECRET_KEY=sua_chave_secreta_muito_segura
DEBUG=True
ENVIRONMENT=development

# Banco de Dados
DATABASE_URL=sqlite:///./finance_app.db

# OAuth Google
GOOGLE_CLIENT_ID=seu_google_client_id
GOOGLE_CLIENT_SECRET=seu_google_client_secret
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/google/callback

# IA
OLLAMA_BASE_URL=http://localhost:11434
AI_MODEL=llama3:latest
AI_ENABLED=True

# Email (opcional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=sua_senha_app
```

### OAuth Google
1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um projeto ou selecione existente
3. Ative a API Google OAuth2
4. Configure credenciais OAuth 2.0:
   - Tipo: Aplicação Web
   - URIs de redirecionamento: `http://127.0.0.1:8000/auth/google/callback`
5. Salve credenciais em arquivo externo `oauth.txt`:
```
GOOGLE_CLIENT_ID=seu_client_id
GOOGLE_CLIENT_SECRET=seu_client_secret
```

### Configuração de Produção
```bash
# Backend com Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker

# Frontend build
npm run build

# Nginx (exemplo)
server {
    listen 80;
    server_name seu-dominio.com;

    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📱 Guia de Uso

### 1. Primeiro Acesso
1. **Cadastro**: Crie conta com email/senha ou Google
2. **Configuração**: Complete perfil e preferências
3. **Família**: Convide membros (opcional)

### 2. Gestão de Transações
1. **Adicionar**: Use botão flutuante (+) ou menu
2. **Categorizar**: IA sugere categoria automaticamente
3. **Proprietário**: Selecione dono da transação
4. **Editar**: Clique na transação para modificar

### 3. Filtros e Análises
1. **Filtros Globais**: Configure período, tipo, categoria
2. **Persistência**: Filtros mantidos entre páginas
3. **Análises**: Acesse página dedicada para gráficos
4. **Exportação**: Baixe relatórios em PDF/Excel

### 4. Processamento de Documentos
1. **Upload**: Arraste arquivos ou clique para selecionar
2. **Validação**: Sistema verifica e sugere correções
3. **Aprovação**: Revise transações antes de salvar
4. **Duplicatas**: Sistema previne automaticamente

### 5. Planejamento Financeiro
1. **Metas**: Defina objetivos com prazo e valor
2. **Orçamentos**: Estabeleça limites por categoria
3. **IA**: Receba sugestões personalizadas
4. **Acompanhamento**: Monitore progresso

## 🔒 Segurança e Privacidade

### Autenticação
- **JWT Tokens**: Autenticação stateless
- **OAuth2**: Integração segura com Google
- **Refresh Tokens**: Renovação automática
- **Rate Limiting**: Proteção contra ataques

### Dados
- **Criptografia**: Dados sensíveis criptografados
- **IA Local**: Processamento sem envio para nuvem
- **Backup**: Sistema de backup automático
- **LGPD**: Conformidade com lei de proteção de dados

### Permissões
- **Roles**: Admin e usuário padrão
- **Família**: Controle granular de acesso
- **Auditoria**: Log de todas as ações
- **Isolamento**: Dados familiares isolados

## 🚀 Funcionalidades Avançadas

### Sistema de Filtros Globais
- **Persistência**: Filtros salvos no localStorage
- **Sincronização**: Aplicados em todas as páginas
- **Performance**: Otimização para grandes volumes
- **UX**: Interface intuitiva com chips visuais

### Navegação Inteligente
- **Responsiva**: Adapta-se ao scroll do usuário
- **Animações**: Transições suaves
- **Acessibilidade**: Suporte a leitores de tela
- **Mobile**: Otimizada para dispositivos móveis

### IA Contextual
- **Aprendizado**: Melhora com uso
- **Contexto**: Considera histórico do usuário
- **Privacidade**: Processamento 100% local
- **Personalização**: Adapta-se ao perfil familiar

## 🧪 Testes e Qualidade

### Backend
```bash
# Testes unitários
pytest tests/

# Cobertura
pytest --cov=app tests/

# Linting
flake8 app/
black app/
```

### Frontend
```bash
# Testes
npm test

# Cobertura
npm run test:coverage

# Linting
npm run lint
npm run lint:fix
```

### Integração
```bash
# Testes E2E
npm run test:e2e

# Performance
npm run test:performance
```

## 📈 Monitoramento e Logs

### Métricas
- **Performance**: Tempo de resposta da API
- **Uso**: Estatísticas de funcionalidades
- **Erros**: Tracking automático de problemas
- **Usuários**: Análise de comportamento

### Logs
- **Estruturados**: JSON para facilitar análise
- **Níveis**: DEBUG, INFO, WARNING, ERROR
- **Rotação**: Arquivos com rotação automática
- **Alertas**: Notificações para erros críticos

## 🔄 Atualizações e Manutenção

### Versionamento
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Changelog**: Registro detalhado de mudanças
- **Migrations**: Atualizações de banco automáticas
- **Rollback**: Possibilidade de reverter versões

### Backup
```bash
# Backup manual
python scripts/backup.py

# Backup automático (cron)
0 2 * * * /path/to/backup.sh
```

### Monitoramento
- **Health Checks**: Endpoints de saúde
- **Alertas**: Notificações automáticas
- **Métricas**: Dashboard de monitoramento
- **Logs**: Centralização e análise

## 🤝 Contribuição

### Processo de Desenvolvimento
1. **Fork**: Faça fork do repositório
2. **Branch**: Crie branch descritiva (`feature/nova-funcionalidade`)
3. **Commits**: Use conventional commits
4. **Testes**: Adicione testes para novas funcionalidades
5. **PR**: Abra Pull Request com descrição detalhada

### Padrões de Código
- **Python**: PEP 8, Black, isort
- **TypeScript**: ESLint, Prettier
- **Commits**: Conventional Commits
- **Documentação**: Docstrings e comentários

### Issues e Bugs
- **Template**: Use templates fornecidos
- **Labels**: Aplique labels apropriadas
- **Reprodução**: Forneça passos para reproduzir
- **Ambiente**: Especifique versões e SO

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte e Comunidade

### Canais de Suporte
- **GitHub Issues**: Bugs e feature requests
- **Discussions**: Perguntas e discussões
- **Email**: <EMAIL>
- **Discord**: Comunidade de usuários

### Documentação
- **Wiki**: Guias detalhados
- **API Docs**: Documentação automática
- **Tutoriais**: Vídeos e artigos
- **FAQ**: Perguntas frequentes

### Roadmap
- **Q1 2024**: Mobile app nativo
- **Q2 2024**: Integração bancária
- **Q3 2024**: Relatórios avançados
- **Q4 2024**: Machine Learning avançado

---

**Finance App** - Transformando a gestão financeira familiar com tecnologia e inteligência artificial. 🚀💰