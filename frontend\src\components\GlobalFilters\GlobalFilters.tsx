import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Button,
  Chip,
  Typography,
  Paper,
  Grid,
  Collapse,
  IconButton
} from '@mui/material';
import {
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import api from '../../services/api';

export interface GlobalFiltersData {
  startDate: Date | null;
  endDate: Date | null;
  type: string;
  category: string;
  familyMember: string;
}

interface GlobalFiltersProps {
  filters: GlobalFiltersData;
  onFiltersChange: (filters: GlobalFiltersData) => void;
  showFamilyFilter?: boolean;
}

interface FamilyMember {
  id: number;
  name: string;
  email: string;
}

const GlobalFilters: React.FC<GlobalFiltersProps> = ({
  filters,
  onFiltersChange,
  showFamilyFilter = true
}) => {
  const [expanded, setExpanded] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);

  useEffect(() => {
    loadCategories();
    if (showFamilyFilter) {
      loadFamilyMembers();
    }
  }, [showFamilyFilter]);

  const loadCategories = async () => {
    try {
      const response = await api.get('/transactions/categories');
      setCategories(response.data.categories || []);
    } catch (error) {
      console.error('Erro ao carregar categorias:', error);
    }
  };

  const loadFamilyMembers = async () => {
    try {
      const response = await api.get('/family/members');
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar membros da família:', error);
    }
  };

  const handleFilterChange = (field: keyof GlobalFiltersData, value: any) => {
    onFiltersChange({
      ...filters,
      [field]: value
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      startDate: null,
      endDate: null,
      type: '',
      category: '',
      familyMember: ''
    });
  };

  const hasActiveFilters = () => {
    return filters.startDate || filters.endDate || filters.type || filters.category || filters.familyMember;
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.startDate) count++;
    if (filters.endDate) count++;
    if (filters.type) count++;
    if (filters.category) count++;
    if (filters.familyMember) count++;
    return count;
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Paper sx={{ p: 2, mb: 3, backgroundColor: '#f8f9fa' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6" color="primary">
              Filtros
            </Typography>
            {hasActiveFilters() && (
              <Chip
                label={`${getActiveFiltersCount()} ativo${getActiveFiltersCount() > 1 ? 's' : ''}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {hasActiveFilters() && (
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                color="secondary"
              >
                Limpar
              </Button>
            )}
            <IconButton
              onClick={() => setExpanded(!expanded)}
              size="small"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              {/* Data Inicial */}
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Data Inicial"
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              </Grid>

              {/* Data Final */}
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Data Final"
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              </Grid>

              {/* Tipo */}
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  fullWidth
                  size="small"
                  label="Tipo"
                  value={filters.type}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                >
                  <MenuItem value="">Todos</MenuItem>
                  <MenuItem value="income">Receita</MenuItem>
                  <MenuItem value="expense">Despesa</MenuItem>
                  <MenuItem value="investment">Investimento</MenuItem>
                </TextField>
              </Grid>

              {/* Categoria */}
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  fullWidth
                  size="small"
                  label="Categoria"
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  <MenuItem value="">Todas</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {/* Membro da Família */}
              {showFamilyFilter && (
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    select
                    fullWidth
                    size="small"
                    label="Membro"
                    value={filters.familyMember}
                    onChange={(e) => handleFilterChange('familyMember', e.target.value)}
                  >
                    <MenuItem value="">Todos</MenuItem>
                    <MenuItem value="self">Você</MenuItem>
                    {familyMembers.map((member) => (
                      <MenuItem key={member.id} value={member.id.toString()}>
                        {member.name || member.email}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              )}
            </Grid>

            {/* Filtros Ativos */}
            {hasActiveFilters() && (
              <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1, alignSelf: 'center' }}>
                  Filtros ativos:
                </Typography>
                {filters.startDate && (
                  <Chip
                    label={`De: ${filters.startDate.toLocaleDateString('pt-BR')}`}
                    size="small"
                    onDelete={() => handleFilterChange('startDate', null)}
                  />
                )}
                {filters.endDate && (
                  <Chip
                    label={`Até: ${filters.endDate.toLocaleDateString('pt-BR')}`}
                    size="small"
                    onDelete={() => handleFilterChange('endDate', null)}
                  />
                )}
                {filters.type && (
                  <Chip
                    label={`Tipo: ${filters.type === 'income' ? 'Receita' : filters.type === 'expense' ? 'Despesa' : 'Investimento'}`}
                    size="small"
                    onDelete={() => handleFilterChange('type', '')}
                  />
                )}
                {filters.category && (
                  <Chip
                    label={`Categoria: ${filters.category}`}
                    size="small"
                    onDelete={() => handleFilterChange('category', '')}
                  />
                )}
                {filters.familyMember && (
                  <Chip
                    label={`Membro: ${filters.familyMember === 'self' ? 'Você' : familyMembers.find(m => m.id.toString() === filters.familyMember)?.name || 'Desconhecido'}`}
                    size="small"
                    onDelete={() => handleFilterChange('familyMember', '')}
                  />
                )}
              </Box>
            )}
          </Box>
        </Collapse>
      </Paper>
    </LocalizationProvider>
  );
};

export default GlobalFilters;
