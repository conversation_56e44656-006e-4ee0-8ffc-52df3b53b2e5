from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from ..database import get_db
from ..models.financial import User, CreditCard, CreditCardStatement, Transaction
from ..schemas.financial import (
    CreditCardCreate,
    CreditCard as CreditCardSchema,
    CreditCardUpdate,
    CreditCardStatementCreate,
    CreditCardStatement as CreditCardStatementSchema,
    CreditCardStatementUpdate,
    Transaction as TransactionSchema
)
from ..security import get_current_user
import logging
from datetime import datetime, timedelta
from calendar import monthrange
from sqlalchemy import and_, func

# Configurar logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/credit-cards",
    tags=["credit-cards"],
    responses={404: {"description": "Not found"}}
)

@router.post("/", response_model=CreditCardSchema)
def create_credit_card(
    credit_card: CreditCardCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cria um novo cartão de crédito.
    """
    db_credit_card = CreditCard(
        user_id=current_user.id,
        name=credit_card.name,
        last_digits=credit_card.last_digits,
        closing_day=credit_card.closing_day,
        due_day=credit_card.due_day,
        credit_limit=credit_card.credit_limit,
        current_balance=credit_card.current_balance or 0
    )

    db.add(db_credit_card)
    db.commit()
    db.refresh(db_credit_card)

    logger.info(f"Cartão de crédito criado com sucesso: ID {db_credit_card.id}")
    return db_credit_card

@router.get("/", response_model=List[CreditCardSchema])
def read_credit_cards(
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Retorna todos os cartões de crédito do usuário.
    """
    credit_cards = db.query(CreditCard).filter(
        CreditCard.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    return credit_cards

@router.get("/{credit_card_id}", response_model=CreditCardSchema)
def read_credit_card(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retorna um cartão de crédito específico pelo ID.
    """
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    return db_credit_card

@router.put("/{credit_card_id}", response_model=CreditCardSchema)
def update_credit_card(
    credit_card_id: int,
    credit_card: CreditCardUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Atualiza um cartão de crédito existente.
    """
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Atualizar apenas os campos não nulos
    update_data = credit_card.model_dump(exclude_unset=True, exclude_none=True)

    for field, value in update_data.items():
        setattr(db_credit_card, field, value)

    db.commit()
    db.refresh(db_credit_card)
    return db_credit_card

@router.delete("/{credit_card_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_credit_card(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove um cartão de crédito existente.
    """
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Verificar se há faturas ou transações associadas
    statements = db.query(CreditCardStatement).filter(
        CreditCardStatement.credit_card_id == credit_card_id
    ).all()

    transactions = db.query(Transaction).filter(
        Transaction.credit_card_id == credit_card_id
    ).all()

    # Remover faturas e transações associadas
    for statement in statements:
        db.delete(statement)

    for transaction in transactions:
        transaction.credit_card_id = None

    db.delete(db_credit_card)
    db.commit()
    return None

@router.post("/{credit_card_id}/statements", response_model=CreditCardStatementSchema)
def create_statement(
    credit_card_id: int,
    statement: CreditCardStatementCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cria uma nova fatura para um cartão de crédito.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Criar a fatura
    db_statement = CreditCardStatement(
        credit_card_id=credit_card_id,
        statement_date=statement.statement_date,
        due_date=statement.due_date,
        total_amount=statement.total_amount,
        paid_amount=statement.paid_amount or 0,
        is_paid=statement.is_paid or False,
        payment_date=statement.payment_date,
        payment_transaction_id=statement.payment_transaction_id
    )

    db.add(db_statement)
    db.commit()
    db.refresh(db_statement)

    return db_statement

@router.get("/{credit_card_id}/statements", response_model=List[CreditCardStatementSchema])
def get_statements(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 12,  # Por padrão, retorna as 12 últimas faturas
    db: Session = Depends(get_db)
):
    """
    Retorna as faturas de um cartão de crédito.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    statements = db.query(CreditCardStatement).filter(
        CreditCardStatement.credit_card_id == credit_card_id
    ).order_by(CreditCardStatement.statement_date.desc()).offset(skip).limit(limit).all()

    return statements

@router.get("/{credit_card_id}/statements/{statement_id}", response_model=CreditCardStatementSchema)
def get_statement(
    credit_card_id: int,
    statement_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retorna uma fatura específica de um cartão de crédito.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    statement = db.query(CreditCardStatement).filter(
        CreditCardStatement.id == statement_id,
        CreditCardStatement.credit_card_id == credit_card_id
    ).first()

    if not statement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Fatura não encontrada"
        )

    return statement

@router.put("/{credit_card_id}/statements/{statement_id}", response_model=CreditCardStatementSchema)
def update_statement(
    credit_card_id: int,
    statement_id: int,
    statement_update: CreditCardStatementUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Atualiza uma fatura existente.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    db_statement = db.query(CreditCardStatement).filter(
        CreditCardStatement.id == statement_id,
        CreditCardStatement.credit_card_id == credit_card_id
    ).first()

    if not db_statement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Fatura não encontrada"
        )

    # Atualizar apenas os campos não nulos
    update_data = statement_update.model_dump(exclude_unset=True, exclude_none=True)

    for field, value in update_data.items():
        setattr(db_statement, field, value)

    db.commit()
    db.refresh(db_statement)

    return db_statement

@router.post("/{credit_card_id}/statements/{statement_id}/pay")
def pay_statement(
    credit_card_id: int,
    statement_id: int,
    amount: float = Body(..., embed=True),
    payment_date: Optional[datetime] = Body(None, embed=True),
    create_transaction: bool = Body(True, embed=True),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Registra o pagamento de uma fatura.
    """
    # Verificações básicas
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    db_statement = db.query(CreditCardStatement).filter(
        CreditCardStatement.id == statement_id,
        CreditCardStatement.credit_card_id == credit_card_id
    ).first()

    if not db_statement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Fatura não encontrada"
        )

    # Verificar se o valor de pagamento é válido
    if amount <= 0 or amount > db_statement.total_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Valor de pagamento inválido. O valor deve estar entre 0 e {db_statement.total_amount}"
        )

    # Registrar o pagamento
    payment_date_actual = payment_date or datetime.now()

    # Criar uma transação para o pagamento se solicitado
    transaction_id = None
    if create_transaction:
        transaction = Transaction(
            user_id=current_user.id,
            description=f"Pagamento de fatura - {db_credit_card.name}",
            amount=amount,
            type="expense",
            transaction_type="expense",
            transaction_subtype="credit_card_payment",
            category="Cartão de Crédito",
            date=payment_date_actual,
            credit_card_id=credit_card_id
        )

        db.add(transaction)
        db.commit()
        db.refresh(transaction)
        transaction_id = transaction.id

    # Atualizar a fatura
    db_statement.paid_amount = amount
    db_statement.payment_date = payment_date_actual
    db_statement.payment_transaction_id = transaction_id
    db_statement.is_paid = amount >= db_statement.total_amount

    # Atualizar o saldo do cartão
    db_credit_card.current_balance -= amount
    if db_credit_card.current_balance < 0:
        db_credit_card.current_balance = 0

    db.commit()
    db.refresh(db_statement)

    return {
        "statement_id": db_statement.id,
        "credit_card_id": db_statement.credit_card_id,
        "total_amount": db_statement.total_amount,
        "paid_amount": db_statement.paid_amount,
        "is_paid": db_statement.is_paid,
        "payment_date": db_statement.payment_date,
        "payment_transaction_id": db_statement.payment_transaction_id
    }

@router.get("/{credit_card_id}/transactions", response_model=List[TransactionSchema])
def get_credit_card_transactions(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Retorna todas as transações associadas a um cartão de crédito.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Construir a query base
    query = db.query(Transaction).filter(
        Transaction.credit_card_id == credit_card_id
    )

    # Adicionar filtros de data se fornecidos
    if start_date:
        query = query.filter(Transaction.date >= start_date)

    if end_date:
        query = query.filter(Transaction.date <= end_date)

    # Ordenar por data
    transactions = query.order_by(Transaction.date.desc()).all()

    return transactions

@router.get("/{credit_card_id}/current-statement")
def get_current_statement(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retorna informações sobre a fatura atual (mesmo que ainda não fechada).
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Determinar o período atual da fatura
    today = datetime.now()
    closing_day = db_credit_card.closing_day
    due_day = db_credit_card.due_day

    # Determinar a data de fechamento da fatura atual
    if today.day <= closing_day:
        # Ainda não fechou este mês
        closing_date = datetime(today.year, today.month, closing_day)
    else:
        # Já fechou este mês, então a próxima data de fechamento é no próximo mês
        next_month = today.month + 1 if today.month < 12 else 1
        next_year = today.year if today.month < 12 else today.year + 1
        closing_date = datetime(next_year, next_month, closing_day)

    # Determinar a data da última fatura fechada
    if today.day <= closing_day:
        # A última fatura fechada foi no mês anterior
        prev_month = today.month - 1 if today.month > 1 else 12
        prev_year = today.year if today.month > 1 else today.year - 1
        days_in_month = monthrange(prev_year, prev_month)[1]
        last_closing_day = min(closing_day, days_in_month)
        last_closing_date = datetime(prev_year, prev_month, last_closing_day)
    else:
        # A última fatura fechada foi neste mês
        last_closing_date = datetime(today.year, today.month, closing_day)

    # Determinar a data de vencimento da fatura atual
    if due_day < closing_day:
        # Vencimento no mês seguinte ao fechamento
        due_month = closing_date.month + 1 if closing_date.month < 12 else 1
        due_year = closing_date.year if closing_date.month < 12 else closing_date.year + 1
    else:
        # Vencimento no mesmo mês do fechamento
        due_month = closing_date.month
        due_year = closing_date.year

    days_in_due_month = monthrange(due_year, due_month)[1]
    actual_due_day = min(due_day, days_in_due_month)
    due_date = datetime(due_year, due_month, actual_due_day)

    # Buscar transações do período atual
    current_transactions = db.query(Transaction).filter(
        Transaction.credit_card_id == credit_card_id,
        Transaction.date > last_closing_date,
        Transaction.date <= closing_date
    ).all()

    # Calcular o valor total das transações
    total_amount = sum(t.amount for t in current_transactions)

    # Verificar se já existe uma fatura para este período
    existing_statement = db.query(CreditCardStatement).filter(
        CreditCardStatement.credit_card_id == credit_card_id,
        CreditCardStatement.statement_date == closing_date
    ).first()

    return {
        "credit_card_id": credit_card_id,
        "credit_card_name": db_credit_card.name,
        "current_period_start": last_closing_date,
        "current_period_end": closing_date,
        "due_date": due_date,
        "total_amount": total_amount,
        "transactions_count": len(current_transactions),
        "transactions": current_transactions,
        "is_closed": today > closing_date,
        "has_statement": existing_statement is not None,
        "statement_id": existing_statement.id if existing_statement else None
    }

@router.post("/{credit_card_id}/close-statement")
def close_current_statement(
    credit_card_id: int,
    current_user: User = Depends(get_current_user),
    override_date: Optional[datetime] = Body(None, embed=True),
    db: Session = Depends(get_db)
):
    """
    Fecha a fatura atual do cartão e cria um registro de fatura.
    """
    # Verificar se o cartão existe e pertence ao usuário
    db_credit_card = db.query(CreditCard).filter(
        CreditCard.id == credit_card_id,
        CreditCard.user_id == current_user.id
    ).first()

    if not db_credit_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cartão de crédito não encontrado"
        )

    # Determinar a data de fechamento (hoje ou uma data específica)
    closing_date = override_date or datetime.now()

    # Determinar o início do período (último fechamento)
    last_month = closing_date.month - 1 if closing_date.month > 1 else 12
    last_year = closing_date.year if closing_date.month > 1 else closing_date.year - 1
    days_in_last_month = monthrange(last_year, last_month)[1]
    last_closing_day = min(db_credit_card.closing_day, days_in_last_month)
    period_start = datetime(last_year, last_month, last_closing_day)

    # Verificar se já existe uma fatura para este período
    existing_statement = db.query(CreditCardStatement).filter(
        CreditCardStatement.credit_card_id == credit_card_id,
        func.date(CreditCardStatement.statement_date) == func.date(closing_date)
    ).first()

    if existing_statement:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Já existe uma fatura fechada para este período (ID: {existing_statement.id})"
        )

    # Buscar transações do período
    transactions = db.query(Transaction).filter(
        Transaction.credit_card_id == credit_card_id,
        Transaction.date > period_start,
        Transaction.date <= closing_date
    ).all()

    # Calcular o valor total das transações
    total_amount = sum(t.amount for t in transactions)

    # Determinar a data de vencimento
    if db_credit_card.due_day < db_credit_card.closing_day:
        # Vencimento no mês seguinte ao fechamento
        due_month = closing_date.month + 1 if closing_date.month < 12 else 1
        due_year = closing_date.year if closing_date.month < 12 else closing_date.year + 1
    else:
        # Vencimento no mesmo mês do fechamento
        due_month = closing_date.month
        due_year = closing_date.year

    days_in_due_month = monthrange(due_year, due_month)[1]
    actual_due_day = min(db_credit_card.due_day, days_in_due_month)
    due_date = datetime(due_year, due_month, actual_due_day)

    # Criar a fatura
    statement = CreditCardStatement(
        credit_card_id=credit_card_id,
        statement_date=closing_date,
        due_date=due_date,
        total_amount=total_amount,
        paid_amount=0,
        is_paid=False
    )

    # Atualizar o saldo do cartão
    db_credit_card.current_balance += total_amount

    db.add(statement)
    db.commit()
    db.refresh(statement)

    return {
        "statement_id": statement.id,
        "credit_card_id": statement.credit_card_id,
        "statement_date": statement.statement_date,
        "due_date": statement.due_date,
        "total_amount": statement.total_amount,
        "transactions_count": len(transactions),
        "period_start": period_start,
        "period_end": closing_date
    }