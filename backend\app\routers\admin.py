from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from ..database import get_db
from ..models.financial import User, FamilyLink
from ..schemas.financial import User as UserSchema, UserCreate
from ..security import get_current_admin_user, get_current_active_user, get_password_hash

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
    dependencies=[Depends(get_current_admin_user)]
)

@router.get("/users", response_model=List[UserSchema], summary="Listar todos os usuários")
async def list_all_users(
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user),
    skip: int = 0,
    limit: int = 100
):
    """Lista todos os usuários do sistema (apenas admin)"""
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.post("/users", response_model=UserSchema, summary="Criar novo usuário")
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """Cria um novo usuário (apenas admin)"""
    
    # Verificar se o email já existe
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email já está registrado"
        )
    
    # Criar novo usuário
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.put("/users/{user_id}/toggle-admin", response_model=UserSchema, summary="Alternar status de admin")
async def toggle_admin_status(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """Alterna o status de administrador de um usuário"""
    
    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )
    
    # Não permitir remover admin do usuário principal
    if user.email == "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Não é possível alterar o status admin do usuário principal"
        )
    
    # Alternar status admin
    user.is_admin = not getattr(user, 'is_admin', False)
    user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(user)
    
    return user

@router.put("/users/{user_id}/activate", response_model=UserSchema, summary="Ativar/Desativar usuário")
async def toggle_user_activation(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """Ativa ou desativa um usuário"""
    
    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )
    
    # Não permitir desativar o admin principal
    if user.email == "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Não é possível desativar o usuário administrador principal"
        )
    
    # Alternar status
    user.is_active = not user.is_active
    user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(user)
    
    return user

@router.delete("/users/{user_id}", summary="Deletar usuário")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """Deleta um usuário (apenas admin)"""
    
    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )
    
    # Não permitir deletar o admin principal
    if user.email == "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Não é possível deletar o usuário administrador principal"
        )
    
    db.delete(user)
    db.commit()
    
    return {"message": "Usuário deletado com sucesso"}


# ENDPOINTS PARA GERENCIAR VINCULAÇÕES FAMILIARES

@router.get("/family-links")
async def get_all_family_links(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Lista todas as vinculações familiares (apenas admin)"""

    # Verificar se é admin
    if not getattr(current_user, 'is_admin', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem acessar esta funcionalidade"
        )

    # Buscar todas as vinculações
    links = db.query(FamilyLink).all()

    result = []
    for link in links:
        # Buscar informações dos usuários
        requester = db.query(User).filter(User.id == link.requester_id).first()
        invited = db.query(User).filter(User.id == link.invited_id).first()

        result.append({
            "id": link.id,
            "requester_id": link.requester_id,
            "invited_id": link.invited_id,
            "status": link.status,
            "created_at": link.created_at,
            "updated_at": link.updated_at,
            "requester_name": f"{requester.first_name} {requester.last_name}".strip() if requester else "",
            "requester_email": requester.email if requester else "",
            "invited_name": f"{invited.first_name} {invited.last_name}".strip() if invited else "",
            "invited_email": invited.email if invited else ""
        })

    return result


@router.post("/family-links")
async def create_family_link_admin(
    requester_email: str,
    invited_email: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Cria uma vinculação familiar diretamente (apenas admin)"""

    # Verificar se é admin
    if not getattr(current_user, 'is_admin', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem acessar esta funcionalidade"
        )

    # Buscar usuários
    requester = db.query(User).filter(User.email == requester_email).first()
    invited = db.query(User).filter(User.email == invited_email).first()

    if not requester:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Usuário solicitante não encontrado: {requester_email}"
        )

    if not invited:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Usuário convidado não encontrado: {invited_email}"
        )

    # Verificar se já existe vinculação
    existing_link = db.query(FamilyLink).filter(
        ((FamilyLink.requester_id == requester.id) & (FamilyLink.invited_id == invited.id)) |
        ((FamilyLink.requester_id == invited.id) & (FamilyLink.invited_id == requester.id))
    ).first()

    if existing_link:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Já existe uma vinculação entre estes usuários"
        )

    # Criar vinculação aceita diretamente
    family_link = FamilyLink(
        requester_id=requester.id,
        invited_id=invited.id,
        status="accepted"
    )

    db.add(family_link)
    db.commit()
    db.refresh(family_link)

    return {
        "message": "Vinculação familiar criada com sucesso",
        "link": {
            "id": family_link.id,
            "requester_email": requester_email,
            "invited_email": invited_email,
            "status": family_link.status
        }
    }


@router.delete("/family-links/{link_id}")
async def delete_family_link_admin(
    link_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Remove uma vinculação familiar (apenas admin)"""

    # Verificar se é admin
    if not getattr(current_user, 'is_admin', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem acessar esta funcionalidade"
        )

    # Buscar vinculação
    link = db.query(FamilyLink).filter(FamilyLink.id == link_id).first()
    if not link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vinculação não encontrada"
        )

    db.delete(link)
    db.commit()

    return {"message": "Vinculação familiar removida com sucesso"}

@router.post("/users/{user_id}/link-google", summary="Vincular conta Google")
async def link_google_account_admin(
    user_id: int,
    google_data: dict,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """Vincula uma conta Google a um usuário (admin)"""
    
    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )
    
    # Verificar se o Google ID já está em uso
    if google_data.get("google_id"):
        existing_google_user = db.query(User).filter(
            User.google_id == google_data["google_id"],
            User.id != user_id
        ).first()
        
        if existing_google_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Esta conta Google já está vinculada a outro usuário"
            )
    
    # Atualizar dados do Google
    user.google_id = google_data.get("google_id")
    user.profile_picture = google_data.get("profile_picture")
    user.is_verified = True
    user.updated_at = datetime.utcnow()
    
    # Atualizar nome se não estiver definido
    if not user.first_name and google_data.get("first_name"):
        user.first_name = google_data.get("first_name")
    if not user.last_name and google_data.get("last_name"):
        user.last_name = google_data.get("last_name")
    if not user.full_name and google_data.get("full_name"):
        user.full_name = google_data.get("full_name")
    
    db.commit()
    db.refresh(user)
    
    return {
        "message": "Conta Google vinculada com sucesso",
        "user": user
    }
