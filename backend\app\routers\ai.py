from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from ..database import get_db
from ..models.financial import User
from ..security import get_current_active_user
from ..services.ai_assistance import finance_ai
import logging
import os
from pydantic import BaseModel
from datetime import datetime

# Configuração de logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Modelos Pydantic para validação
class SavingsGoal(BaseModel):
    target_amount: float
    target_date: Optional[str] = None

class CategoryLimit(BaseModel):
    category: str
    limit: float

# Endpoints do assistente de IA
@router.get("/ai/status", summary="Verifica status do serviço de IA")
async def get_ai_status(current_user: User = Depends(get_current_active_user)):
    """
    Verifica se o serviço de IA está disponível e configurado
    """
    # Forçar ativação do módulo de IA para fins de teste
    finance_ai.enabled = True

    is_enabled = finance_ai.enabled
    is_available = await finance_ai.is_available()

    logger.info(f"Verificação de status da IA solicitada por usuário {current_user.id}: enabled={is_enabled}, available={is_available}")

    # Verificar detalhes do modelo
    model_info = {}
    if is_enabled:
        if finance_ai.provider == "ollama":
            model_info = {
                "model": finance_ai.settings.OLLAMA_MODEL,
                "base_url": finance_ai.settings.OLLAMA_BASE_URL,
                "timeout": finance_ai.settings.AI_REQUEST_TIMEOUT,
                "max_tokens": finance_ai.settings.AI_MAX_TOKENS,
                "temperature": finance_ai.settings.AI_TEMPERATURE
            }
        elif finance_ai.provider == "lm_studio":
            model_info = {
                "model": finance_ai.settings.LM_STUDIO_MODEL,
                "base_url": finance_ai.settings.LM_STUDIO_BASE_URL,
                "timeout": finance_ai.settings.AI_REQUEST_TIMEOUT,
                "max_tokens": finance_ai.settings.AI_MAX_TOKENS,
                "temperature": finance_ai.settings.AI_TEMPERATURE
            }

    # Determinar mensagem de status
    if not is_enabled:
        status_message = "Serviço de IA está desativado nas configurações"
    elif not is_available:
        status_message = "Serviço de IA está ativado mas não está disponível no momento"
    else:
        status_message = "Serviço de IA ativo e disponível"

    # Adicionar informações de debug
    debug_info = {
        "env_vars": {
            "AI_ENABLED": os.getenv("AI_ENABLED"),
            "AI_PROVIDER": os.getenv("AI_PROVIDER"),
            "OLLAMA_MODEL": os.getenv("OLLAMA_MODEL")
        },
        "settings_obj": {
            "AI_ENABLED": str(finance_ai.settings.AI_ENABLED),
            "AI_PROVIDER": str(finance_ai.settings.AI_PROVIDER),
            "OLLAMA_MODEL": finance_ai.settings.OLLAMA_MODEL
        },
        "instance_vars": {
            "enabled": str(finance_ai.enabled),
            "provider": str(finance_ai.provider)
        }
    }

    return {
        "enabled": is_enabled,
        "available": is_available,
        "provider": str(finance_ai.provider),
        "message": status_message,
        "model_info": model_info,
        "debug_info": debug_info
    }

@router.get("/ai/insights", summary="Obter insights financeiros")
async def get_financial_insights(
    time_period: str = Query("month", description="Período de tempo para análise (week, month, quarter, year)"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Obtém insights financeiros personalizados com base nas transações do usuário
    """
    logger.info(f"Insights financeiros solicitados por usuário {current_user.id} para período {time_period}")

    # Forçar ativação do módulo de IA para fins de teste
    finance_ai.enabled = True
    logger.info(f"Status do módulo de IA após forçar ativação: enabled={finance_ai.enabled}, provider={finance_ai.provider}")

    try:
        logger.info("Chamando método get_financial_insights do finance_ai")
        insights = await finance_ai.get_financial_insights(
            user_id=current_user.id,
            db=db,
            time_period=time_period
        )
        logger.info(f"Insights obtidos com sucesso: {insights}")
        return insights
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"Erro ao obter insights: {str(e)}")
        logger.error(f"Traceback completo: {error_trace}")

        # Retornar uma resposta de fallback em vez de um erro 500
        return {
            "message": "Não foi possível gerar insights no momento",
            "insights": [
                {
                    "title": "Serviço temporariamente indisponível",
                    "explanation": "Estamos enfrentando dificuldades técnicas para gerar insights. Por favor, tente novamente mais tarde."
                }
            ],
            "summary": {
                "total_income": 0,
                "total_expenses": 0,
                "balance": 0,
                "transaction_count": 0
            }
        }

@router.post("/ai/categorize-transactions", summary="Categorizar transações usando IA")
async def categorize_transactions(
    transactions: List[Dict[str, Any]],
    current_user: User = Depends(get_current_active_user)
):
    """
    Categoriza uma lista de transações usando IA
    """
    logger.info(f"Categorização de {len(transactions)} transações solicitada por usuário {current_user.id}")

    if len(transactions) > 20:
        logger.warning(f"Número excessivo de transações ({len(transactions)}) para categorização")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Limite máximo de 20 transações por requisição"
        )

    try:
        categorized = await finance_ai.categorize_transactions(transactions)
        return {
            "message": f"Categorizadas {len(categorized)} transações",
            "transactions": categorized
        }
    except Exception as e:
        logger.error(f"Erro ao categorizar transações: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao categorizar transações: {str(e)}"
        )

@router.post("/ai/savings-recommendations", summary="Obter recomendações de economia")
async def get_savings_recommendations(
    goal: Optional[SavingsGoal] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Obtém recomendações personalizadas para economizar dinheiro
    """
    logger.info(f"Recomendações de economia solicitadas por usuário {current_user.id}")

    target_amount = None
    target_date = None

    if goal:
        target_amount = goal.target_amount
        target_date = goal.target_date
        logger.info(f"Meta de economia: R$ {target_amount} até {target_date}")

    try:
        recommendations = await finance_ai.get_savings_recommendations(
            user_id=current_user.id,
            db=db,
            target_amount=target_amount,
            target_date=target_date
        )
        return recommendations
    except Exception as e:
        logger.error(f"Erro ao obter recomendações de economia: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar recomendações: {str(e)}"
        )

@router.get("/ai/spending-limits", summary="Obter limites sugeridos de gastos")
async def get_spending_limits(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Obtém limites sugeridos de gastos por categoria baseados no histórico
    """
    logger.info(f"Limites de gastos solicitados por usuário {current_user.id}")

    try:
        limits = await finance_ai.suggest_spending_limits(
            user_id=current_user.id,
            db=db
        )
        return limits
    except Exception as e:
        logger.error(f"Erro ao sugerir limites de gastos: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar limites de gastos: {str(e)}"
        )

@router.post("/ai/set-limits", summary="Definir limites de gastos")
async def set_spending_limits(
    limits: List[CategoryLimit],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Define limites de gastos personalizados por categoria
    """
    # Este é apenas um modelo para implementação futura
    # Você precisará criar uma tabela no banco de dados para armazenar esses limites

    logger.info(f"Definição de limites de gastos solicitada por usuário {current_user.id} para {len(limits)} categorias")

    return {
        "message": "Limites de gastos definidos com sucesso",
        "limits": [
            {"category": limit.category, "limit": limit.limit}
            for limit in limits
        ]
    }

@router.get("/ai/investment-suggestions", summary="Obter sugestões de investimentos")
async def get_investment_suggestions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Obtém sugestões de investimentos baseadas no perfil financeiro do usuário
    """
    logger.info(f"Sugestões de investimentos solicitadas por usuário {current_user.id}")

    try:
        # Usar a mesma lógica do insights, mas com prompt específico para investimentos
        suggestions = await finance_ai.generate_investment_suggestions(
            user_id=current_user.id,
            db=db
        )
        return suggestions
    except Exception as e:
        logger.error(f"Erro ao gerar sugestões de investimentos: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar sugestões de investimentos: {str(e)}"
        )