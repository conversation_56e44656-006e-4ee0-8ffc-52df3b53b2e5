from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from ..database import get_db
from ..models.financial import User, FinancialGoal
from ..schemas.financial import FinancialGoalCreate, FinancialGoal as FinancialGoalResponse, FinancialGoalUpdate
from ..dependencies import get_current_active_user
import logging

router = APIRouter(
    prefix="/financial-goals",
    tags=["financial-goals"],
)

logger = logging.getLogger(__name__)


@router.get("/", response_model=List[FinancialGoalResponse])
async def get_financial_goals(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Retorna todas as metas financeiras do usuário.
    """
    try:
        goals = db.query(FinancialGoal).filter(
            FinancialGoal.user_id == current_user.id
        ).offset(skip).limit(limit).all()
        return goals
    except Exception as e:
        logger.error(f"Erro ao buscar metas financeiras: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao buscar metas financeiras"
        )


@router.post("/", response_model=FinancialGoalResponse)
async def create_financial_goal(
    goal: FinancialGoalCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Cria uma nova meta financeira.
    """
    try:
        db_goal = FinancialGoal(
            **goal.dict(),
            user_id=current_user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(db_goal)
        db.commit()
        db.refresh(db_goal)
        return db_goal
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao criar meta financeira: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao criar meta financeira"
        )


@router.get("/{goal_id}", response_model=FinancialGoalResponse)
async def get_financial_goal(
    goal_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Retorna uma meta financeira específica.
    """
    goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()
    
    if not goal:
        raise HTTPException(
            status_code=404,
            detail="Meta financeira não encontrada"
        )
    
    return goal


@router.put("/{goal_id}", response_model=FinancialGoalResponse)
async def update_financial_goal(
    goal_id: int,
    goal_update: FinancialGoalUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Atualiza uma meta financeira.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()
    
    if not db_goal:
        raise HTTPException(
            status_code=404,
            detail="Meta financeira não encontrada"
        )
    
    try:
        for key, value in goal_update.dict(exclude_unset=True).items():
            setattr(db_goal, key, value)
        
        db_goal.updated_at = datetime.now()
        db.commit()
        db.refresh(db_goal)
        return db_goal
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao atualizar meta financeira: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao atualizar meta financeira"
        )


@router.delete("/{goal_id}")
async def delete_financial_goal(
    goal_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Exclui uma meta financeira.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()
    
    if not db_goal:
        raise HTTPException(
            status_code=404,
            detail="Meta financeira não encontrada"
        )
    
    try:
        db.delete(db_goal)
        db.commit()
        return {"message": "Meta financeira excluída com sucesso"}
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao excluir meta financeira: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao excluir meta financeira"
        )


@router.post("/{goal_id}/contribute")
async def contribute_to_goal(
    goal_id: int,
    amount: float,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Adicionar uma contribuição a uma meta financeira.
    """
    db_goal = db.query(FinancialGoal).filter(
        FinancialGoal.id == goal_id,
        FinancialGoal.user_id == current_user.id
    ).first()
    
    if not db_goal:
        raise HTTPException(
            status_code=404,
            detail="Meta financeira não encontrada"
        )
    
    try:
        db_goal.current_amount += amount
        db_goal.updated_at = datetime.now()
        db.commit()
        db.refresh(db_goal)
        
        # Verificar se a meta foi atingida
        if db_goal.current_amount >= db_goal.target_amount and not db_goal.completed:
            db_goal.completed = True
            db_goal.completion_date = datetime.now()
            db.commit()
            db.refresh(db_goal)
            
        return {
            "message": "Contribuição adicionada com sucesso",
            "goal": db_goal
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Erro ao adicionar contribuição: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro ao adicionar contribuição"
        )