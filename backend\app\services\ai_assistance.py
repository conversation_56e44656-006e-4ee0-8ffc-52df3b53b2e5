import logging
import httpx
import json
import re
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import asyncio

from ..config import settings
from sqlalchemy.orm import Session
from ..models.financial import Transaction
import pandas as pd
from enum import Enum
from fastapi import HTTPException, status

# Configuração de logging
logger = logging.getLogger(__name__)

# Configuração de idioma para prompts de IA
LANGUAGE = "português brasileiro"
LANGUAGE_INSTRUCTION = f"Responda sempre em {LANGUAGE}, usando linguagem clara e acessível."

# Configurações do módulo de IA
class AIProviderType(str, Enum):
    OLLAMA = "ollama"
    LM_STUDIO = "lm_studio"
    OPENAI = "openai"
    DISABLED = "disabled"

# Usar configurações centralizadas do settings



# Usar configurações centralizadas
logger.info(f"Configurações de IA carregadas: AI_ENABLED={settings.AI_ENABLED}, AI_PROVIDER={settings.AI_PROVIDER}")

# Classe base do assistente de IA
class FinanceAIAssistant:
    """
    Classe para assistente de IA que ajuda com finanças pessoais
    """

    def __init__(self):
        # Usar configurações centralizadas
        self.enabled = settings.AI_ENABLED
        self.provider = settings.AI_PROVIDER

        logger.info(f"Inicializando módulo de IA com configurações: enabled={self.enabled}, provider={self.provider}, model={settings.OLLAMA_MODEL}")

        if not self.enabled:
            logger.info("Módulo de IA está desativado")
        else:
            logger.info(f"Módulo de IA inicializado com provedor: {self.provider}")

    async def is_available(self) -> bool:
        """
        Verifica se o serviço de IA está disponível

        Returns:
            True se disponível, False caso contrário
        """
        if not self.enabled:
            logger.info("Módulo de IA está desativado, retornando False para is_available")
            return False

        try:
            logger.info(f"Verificando disponibilidade do provedor: {self.provider}")

            if self.provider == AIProviderType.OLLAMA:
                url = f"{settings.OLLAMA_BASE_URL}/api/tags"
                logger.info(f"Verificando disponibilidade do Ollama em: {url}")

                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=5.0)
                    available = response.status_code == 200

                    if available:
                        # Verificar se o modelo está disponível
                        models = response.json().get("models", [])
                        model_names = [m.get("name") for m in models]
                        logger.info(f"Modelos disponíveis no Ollama: {model_names}")

                        # Verificar se o modelo está disponível (com correspondência exata ou parcial)
                        model_available = settings.OLLAMA_MODEL in model_names

                        # Se não encontrou correspondência exata, verificar correspondência parcial
                        if not model_available:
                            for model_name in model_names:
                                if settings.OLLAMA_MODEL in model_name or model_name in settings.OLLAMA_MODEL:
                                    logger.info(f"Encontrada correspondência parcial para o modelo {settings.OLLAMA_MODEL}: {model_name}")
                                    # Atualizar o modelo para o nome correto
                                    settings.OLLAMA_MODEL = model_name
                                    model_available = True
                                    break

                        if not model_available:
                            logger.warning(f"Modelo {settings.OLLAMA_MODEL} não encontrado. Modelos disponíveis: {model_names}")
                            return False

                        logger.info(f"Ollama disponível e modelo {settings.OLLAMA_MODEL} encontrado")
                    else:
                        logger.warning(f"Ollama não disponível. Status: {response.status_code}")

                    return available

            elif self.provider == AIProviderType.LM_STUDIO:
                url = f"{settings.LM_STUDIO_BASE_URL}/models"
                logger.info(f"Verificando disponibilidade do LM Studio em: {url}")

                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=5.0)
                    available = response.status_code == 200

                    if available:
                        logger.info("LM Studio disponível")
                    else:
                        logger.warning(f"LM Studio não disponível. Status: {response.status_code}")

                    return available

            logger.warning(f"Provedor não suportado: {self.provider}")
            return False

        except httpx.ConnectError as e:
            logger.error(f"Erro de conexão ao verificar disponibilidade do serviço de IA: {str(e)}")
            return False
        except httpx.TimeoutException as e:
            logger.error(f"Timeout ao verificar disponibilidade do serviço de IA: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Erro ao verificar disponibilidade do serviço de IA: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    async def get_financial_insights(
        self,
        user_id: int,
        db: Session,
        time_period: str = "month"
    ) -> Dict[str, Any]:
        """
        Obtém insights financeiros para o usuário

        Args:
            user_id: ID do usuário
            db: Sessão do banco de dados
            time_period: Período de tempo para análise (week, month, year)

        Returns:
            Dicionário com insights financeiros
        """
        # Forçar ativação do módulo de IA para fins de teste
        self.enabled = True

        # Verificar se o módulo está ativado (apenas para log)
        if not self.enabled:
            logger.warning("Tentativa de usar o módulo de IA quando está desativado")
            return {
                "message": "O serviço de IA está desativado",
                "insights": [
                    {
                        "title": "Serviço de IA desativado",
                        "explanation": "O serviço de IA está desativado nas configurações do sistema. Entre em contato com o administrador para ativá-lo."
                    }
                ],
                "summary": {
                    "total_income": 0,
                    "total_expenses": 0,
                    "balance": 0,
                    "transaction_count": 0
                }
            }

        # Verificar se o serviço de IA está disponível
        is_available = await self.is_available()
        if not is_available:
            logger.warning("Serviço de IA não está disponível")
            return {
                "message": "Serviço de IA temporariamente indisponível",
                "insights": [
                    {
                        "title": "Serviço temporariamente indisponível",
                        "explanation": "O serviço de IA está temporariamente indisponível. Por favor, tente novamente mais tarde."
                    }
                ],
                "summary": {
                    "total_income": 0,
                    "total_expenses": 0,
                    "balance": 0,
                    "transaction_count": 0
                }
            }

        # Obter transações do usuário
        logger.info(f"Obtendo transações do usuário {user_id} para o período {time_period}")
        transactions = await self._get_user_transactions(user_id, db, time_period)

        if not transactions:
            logger.info(f"Nenhuma transação encontrada para o usuário {user_id} no período {time_period}")
            return {
                "message": "Não há transações suficientes para análise",
                "insights": [
                    {
                        "title": "Sem dados suficientes",
                        "explanation": "Não há transações suficientes para gerar insights. Adicione mais transações para obter análises personalizadas."
                    }
                ],
                "summary": {
                    "total_income": 0,
                    "total_expenses": 0,
                    "balance": 0,
                    "transaction_count": 0
                }
            }

        logger.info(f"Encontradas {len(transactions)} transações para análise")

        # Preparar dados para análise
        try:
            logger.info("Preparando dados financeiros para análise")
            financial_data = self._prepare_financial_data(transactions)

            # Gerar prompt para a IA
            logger.info("Gerando prompt para a IA")
            prompt = self._generate_financial_prompt(financial_data, time_period)

            # Obter resposta da IA
            logger.info("Enviando consulta para a IA")
            ai_response = await self._query_ai(prompt)

            logger.info("Processando resposta da IA")
            insights = self._parse_ai_response(ai_response)

            logger.info(f"Análise concluída com sucesso. {len(insights)} insights gerados")
            return {
                "message": "Análise financeira concluída com sucesso",
                "insights": insights,
                "summary": financial_data["summary"]
            }

        except Exception as e:
            logger.error(f"Erro ao obter insights financeiros: {str(e)}")
            logger.error(f"Detalhes do erro: {traceback.format_exc()}")

            # Retornar uma resposta de fallback em vez de um erro
            return {
                "message": "Não foi possível gerar insights no momento",
                "insights": [
                    {
                        "title": "Serviço temporariamente indisponível",
                        "explanation": "Estamos enfrentando dificuldades técnicas para gerar insights. Por favor, tente novamente mais tarde."
                    }
                ],
                "summary": financial_data["summary"] if 'financial_data' in locals() else {
                    "total_income": 0,
                    "total_expenses": 0,
                    "balance": 0,
                    "transaction_count": 0
                }
            }

    async def categorize_transactions(
        self,
        transactions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Categoriza transações usando IA

        Args:
            transactions: Lista de transações a serem categorizadas

        Returns:
            Lista de transações com categorias sugeridas
        """
        if not self.enabled:
            return transactions

        try:
            # Se houver poucas transações, processar em lote único
            if len(transactions) <= 5:
                return await self._categorize_batch(transactions)

            # Caso contrário, dividir em lotes para evitar tokens excessivos
            categorized = []
            batch_size = 5

            for i in range(0, len(transactions), batch_size):
                batch = transactions[i:i+batch_size]
                categorized_batch = await self._categorize_batch(batch)
                categorized.extend(categorized_batch)

                # Pequena pausa para não sobrecarregar a API
                await asyncio.sleep(1)

            return categorized

        except Exception as e:
            logger.error(f"Erro ao categorizar transações: {str(e)}")
            # Retornar as transações originais em caso de erro
            return transactions

    async def get_savings_recommendations(
        self,
        user_id: int,
        db: Session,
        target_amount: Optional[float] = None,
        target_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Gera recomendações de economia com base nas transações do usuário

        Args:
            user_id: ID do usuário
            db: Sessão do banco de dados
            target_amount: Valor alvo para economia (opcional)
            target_date: Data alvo para atingir a economia (opcional)

        Returns:
            Dicionário com recomendações de economia
        """
        if not self.enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="O serviço de IA está desativado"
            )

        # Obter transações do último ano
        transactions = await self._get_user_transactions(user_id, db, "year")

        if not transactions:
            return {
                "message": "Não há transações suficientes para análise",
                "recommendations": []
            }

        # Preparar dados para análise
        financial_data = self._prepare_financial_data(transactions)

        # Adicionar informações sobre metas
        goal_info = {}
        if target_amount:
            goal_info["target_amount"] = target_amount
        if target_date:
            goal_info["target_date"] = target_date

        # Gerar prompt para a IA
        prompt = self._generate_savings_prompt(financial_data, goal_info)

        # Obter resposta da IA
        try:
            ai_response = await self._query_ai(prompt)
            recommendations = self._parse_ai_response(ai_response)

            return {
                "message": "Recomendações de economia geradas com sucesso",
                "recommendations": recommendations,
                "current_spending_patterns": financial_data["spending_by_category"]
            }

        except Exception as e:
            logger.error(f"Erro ao gerar recomendações de economia: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erro ao gerar recomendações: {str(e)}"
            )

    async def suggest_spending_limits(
        self,
        user_id: int,
        db: Session
    ) -> Dict[str, Any]:
        """
        Sugere limites de gastos por categoria com base no histórico

        Args:
            user_id: ID do usuário
            db: Sessão do banco de dados

        Returns:
            Dicionário com limites sugeridos por categoria
        """
        # Forçar ativação do módulo de IA para fins de teste
        self.enabled = True

        if not self.enabled:
            return {
                "message": "O serviço de IA está desativado",
                "limits": []
            }

        # Obter transações dos últimos 3 meses
        transactions = await self._get_user_transactions(user_id, db, "quarter")

        if len(transactions) < 10:  # Exigir pelo menos 10 transações para análise
            return {
                "message": "Não há transações suficientes para análise",
                "limits": []
            }

        # Analisar padrões de gastos por categoria
        df = pd.DataFrame([
            {
                "amount": t.amount,
                "category": t.category,
                "type": t.type,
                "date": t.date
            }
            for t in transactions if t.type == "expense"
        ])

        if df.empty:
            return {
                "message": "Não há despesas registradas para análise",
                "limits": []
            }

        # Agrupar por categoria e calcular estatísticas
        spending_by_category = df.groupby("category")["amount"].agg(
            ["sum", "mean", "count", "std"]
        ).reset_index()

        # Adicionar uma margem de 20% acima da média para o limite sugerido
        spending_by_category["suggested_limit"] = spending_by_category["mean"] * 1.2

        # Gerar prompt para a IA
        prompt = f"""
        Você é um assistente financeiro especializado em análise de finanças pessoais.
        {LANGUAGE_INSTRUCTION}

        Com base nos dados de gastos abaixo, sugira limites mensais para cada categoria.

        Estatísticas de gastos por categoria nos últimos 3 meses:
        {spending_by_category.to_string()}

        Considere os seguintes pontos:
        1. Os limites devem ser realistas com base no histórico
        2. Priorize categorias essenciais (alimentação, moradia, saúde)
        3. Sugira reduções em categorias não essenciais quando apropriado
        4. Forneça uma breve justificativa para cada limite

        Retorne sua resposta no formato JSON com a seguinte estrutura:
        {{"category_limits": [
            {{"category": "categoria1", "limit": valor, "justification": "justificativa"}},
            ...
        ]}}
        """

        # Obter resposta da IA
        try:
            ai_response = await self._query_ai(prompt)
            limits_data = json.loads(ai_response)

            # Limpar valores inválidos antes de retornar
            spending_stats = spending_by_category.to_dict(orient="records")
            for stat in spending_stats:
                for key, value in stat.items():
                    if isinstance(value, float):
                        if pd.isna(value) or pd.isinf(value):
                            stat[key] = 0.0
                        else:
                            stat[key] = round(float(value), 2)

            return {
                "message": "Limites de gastos sugeridos com sucesso",
                "limits": limits_data.get("category_limits", []),
                "spending_statistics": spending_stats
            }

        except json.JSONDecodeError:
            logger.error(f"Resposta da IA não é um JSON válido: {ai_response}")
            # Fallback: retornar limites baseados apenas em estatísticas
            fallback_limits = []
            for _, row in spending_by_category.iterrows():
                limit_value = row["suggested_limit"]
                if pd.isna(limit_value) or pd.isinf(limit_value):
                    limit_value = 0.0
                else:
                    limit_value = round(float(limit_value), 2)

                fallback_limits.append({
                    "category": row["category"],
                    "limit": limit_value,
                    "justification": "Baseado na média de gastos históricos com margem de 20%"
                })

            # Limpar valores inválidos nas estatísticas
            spending_stats = spending_by_category.to_dict(orient="records")
            for stat in spending_stats:
                for key, value in stat.items():
                    if isinstance(value, float):
                        if pd.isna(value) or pd.isinf(value):
                            stat[key] = 0.0
                        else:
                            stat[key] = round(float(value), 2)

            return {
                "message": "Limites gerados com base em estatísticas (IA indisponível)",
                "limits": fallback_limits,
                "spending_statistics": spending_stats
            }
        except Exception as e:
            logger.error(f"Erro ao sugerir limites de gastos: {str(e)}")
            logger.error(traceback.format_exc())
            # Retornar fallback em vez de erro
            return {
                "message": "Erro ao gerar limites de gastos",
                "limits": [],
                "error": str(e)
            }

    async def generate_investment_suggestions(
        self,
        user_id: int,
        db: Session
    ) -> Dict[str, Any]:
        """
        Gera sugestões de investimentos baseadas no perfil financeiro do usuário

        Args:
            user_id: ID do usuário
            db: Sessão do banco de dados

        Returns:
            Dicionário com sugestões de investimentos
        """
        if not self.enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="O serviço de IA está desativado"
            )

        # Obter transações dos últimos 6 meses para análise de perfil
        transactions = await self._get_user_transactions(user_id, db, "quarter")

        if len(transactions) < 5:
            return {
                "message": "Não há transações suficientes para análise de investimentos",
                "suggestions": []
            }

        # Preparar dados financeiros
        financial_data = self._prepare_financial_data(transactions)

        # Obter investimentos existentes
        from ..models.financial import Investment
        existing_investments = db.query(Investment).filter(
            Investment.user_id == user_id
        ).all()

        # Preparar dados de investimentos existentes
        investment_data = []
        total_invested = 0
        if existing_investments:
            for inv in existing_investments:
                investment_data.append({
                    "name": inv.name,
                    "type": inv.type,
                    "current_amount": inv.current_amount,
                    "initial_amount": inv.initial_amount
                })
                total_invested += inv.current_amount

        # Gerar prompt para sugestões de investimentos
        prompt = self._generate_investment_prompt(financial_data, investment_data, total_invested)

        try:
            ai_response = await self._query_ai(prompt)
            suggestions = self._parse_ai_response(ai_response)

            return {
                "message": "Sugestões de investimentos geradas com sucesso",
                "suggestions": suggestions,
                "current_financial_profile": {
                    "monthly_balance": financial_data["summary"]["balance"],
                    "total_invested": total_invested,
                    "investment_percentage": (total_invested / max(financial_data["summary"]["total_income"], 1)) * 100
                }
            }

        except Exception as e:
            logger.error(f"Erro ao gerar sugestões de investimentos: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erro ao gerar sugestões de investimentos: {str(e)}"
            )

    # ====== Métodos internos ======

    async def _get_user_transactions(
        self,
        user_id: int,
        db: Session,
        time_period: str = "month"
    ) -> List[Transaction]:
        """Obtém transações do usuário para um período específico"""
        # Determinar a data inicial com base no período
        now = datetime.now()

        if time_period == "week":
            start_date = now - timedelta(days=7)
        elif time_period == "month":
            start_date = now - timedelta(days=30)
        elif time_period == "quarter":
            start_date = now - timedelta(days=90)
        elif time_period == "year":
            start_date = now - timedelta(days=365)
        else:
            start_date = now - timedelta(days=30)  # Default para mês

        # Buscar transações no banco de dados
        transactions = db.query(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.date >= start_date
        ).all()

        return transactions

    def _prepare_financial_data(self, transactions: List[Transaction]) -> Dict[str, Any]:
        """Prepara dados financeiros para análise"""
        # Converter para DataFrame para facilitar a análise
        df = pd.DataFrame([
            {
                "id": t.id,
                "description": t.description,
                "amount": t.amount,
                "type": t.type,
                "category": t.category,
                "date": t.date,
                "subtype": t.subtype
            }
            for t in transactions
        ])

        # Se não houver transações, retornar dados vazios
        if df.empty:
            return {
                "summary": {
                    "total_income": 0,
                    "total_expenses": 0,
                    "balance": 0,
                    "transaction_count": 0
                },
                "spending_by_category": {},
                "income_by_category": {},
                "recent_transactions": []
            }

        # Separar receitas e despesas
        income_df = df[df["type"] == "income"]
        expense_df = df[df["type"] == "expense"]

        # Calcular totais
        total_income = income_df["amount"].sum() if not income_df.empty else 0
        total_expenses = expense_df["amount"].sum() if not expense_df.empty else 0
        balance = total_income - total_expenses

        # Agrupar por categoria
        if not expense_df.empty:
            spending_by_category = expense_df.groupby("category")["amount"].sum().to_dict()
        else:
            spending_by_category = {}

        if not income_df.empty:
            income_by_category = income_df.groupby("category")["amount"].sum().to_dict()
        else:
            income_by_category = {}

        # Transações recentes (últimas 10)
        recent_transactions = df.sort_values("date", ascending=False).head(10).to_dict(orient="records")

        return {
            "summary": {
                "total_income": float(total_income),
                "total_expenses": float(total_expenses),
                "balance": float(balance),
                "transaction_count": len(df)
            },
            "spending_by_category": spending_by_category,
            "income_by_category": income_by_category,
            "recent_transactions": recent_transactions
        }

    def _generate_financial_prompt(
        self,
        financial_data: Dict[str, Any],
        time_period: str
    ) -> str:
        """Gera um prompt para análise financeira"""
        summary = financial_data["summary"]
        spending_by_category = financial_data["spending_by_category"]
        income_by_category = financial_data["income_by_category"]

        # Formatar dados para o prompt
        spending_text = "\n".join([f"- {category}: R$ {amount:.2f}" for category, amount in spending_by_category.items()])
        income_text = "\n".join([f"- {category}: R$ {amount:.2f}" for category, amount in income_by_category.items()])

        period_text = {
            "week": "na última semana",
            "month": "no último mês",
            "quarter": "no último trimestre",
            "year": "no último ano"
        }.get(time_period, "recentemente")

        prompt = f"""
        Você é um assistente financeiro especializado em análise de finanças pessoais.
        {LANGUAGE_INSTRUCTION}

        Analise os dados financeiros abaixo {period_text} e forneça insights úteis, incluindo:
        1. Análise do balanço entre receitas e despesas
        2. Categorias com maior gasto
        3. Oportunidades de economia
        4. Tendências notáveis
        5. Recomendações específicas

        ## Resumo Financeiro:
        - Receita total: R$ {summary["total_income"]:.2f}
        - Despesas totais: R$ {summary["total_expenses"]:.2f}
        - Saldo: R$ {summary["balance"]:.2f}
        - Número de transações: {summary["transaction_count"]}

        ## Despesas por Categoria:
        {spending_text}

        ## Receitas por Categoria:
        {income_text}

        Forneça sua análise em formato de lista com 3-5 insights principais.
        Cada insight deve ter um título claro e uma explicação detalhada.

        IMPORTANTE: Sua resposta DEVE estar no formato JSON exatamente como mostrado abaixo:

        ```json
        {{
          "insights": [
            {{"title": "Título do Insight 1", "explanation": "Explicação detalhada do insight 1"}},
            {{"title": "Título do Insight 2", "explanation": "Explicação detalhada do insight 2"}},
            {{"title": "Título do Insight 3", "explanation": "Explicação detalhada do insight 3"}}
          ]
        }}
        ```

        Foque em insights práticos e acionáveis que ajudem o usuário a melhorar sua saúde financeira.
        Seja específico e direto em suas recomendações.
        """

        return prompt

    def _generate_savings_prompt(
        self,
        financial_data: Dict[str, Any],
        goal_info: Dict[str, Any]
    ) -> str:
        """Gera um prompt para recomendações de economia"""
        summary = financial_data["summary"]
        spending_by_category = financial_data["spending_by_category"]

        # Formatar dados para o prompt
        spending_text = "\n".join([f"- {category}: R$ {amount:.2f}" for category, amount in spending_by_category.items()])

        # Adicionar informações sobre metas, se disponíveis
        goal_text = ""
        if "target_amount" in goal_info and "target_date" in goal_info:
            goal_text = f"""
            ## Meta de Economia:
            - Valor alvo: R$ {goal_info["target_amount"]:.2f}
            - Data alvo: {goal_info["target_date"]}
            """
        elif "target_amount" in goal_info:
            goal_text = f"""
            ## Meta de Economia:
            - Valor alvo: R$ {goal_info["target_amount"]:.2f}
            """

        prompt = f"""
        Você é um assistente financeiro especializado em análise de finanças pessoais.
        {LANGUAGE_INSTRUCTION}

        Com base nos dados financeiros abaixo, forneça recomendações específicas para economizar dinheiro.

        ## Resumo Financeiro:
        - Receita total: R$ {summary["total_income"]:.2f}
        - Despesas totais: R$ {summary["total_expenses"]:.2f}
        - Saldo: R$ {summary["balance"]:.2f}

        ## Despesas por Categoria:
        {spending_text}

        {goal_text}

        Forneça 5 recomendações práticas e específicas para economia.
        Cada recomendação deve ter um título, uma explicação e o potencial de economia estimado (em R$).
        Responda no formato JSON:

        {{
          "recommendations": [
            {{
              "title": "Título da recomendação",
              "explanation": "Explicação detalhada",
              "estimated_savings": valor_numérico
            }},
            ...
          ]
        }}
        """

        return prompt

    def _generate_investment_prompt(
        self,
        financial_data: Dict[str, Any],
        investment_data: List[Dict[str, Any]],
        total_invested: float
    ) -> str:
        """Gera um prompt para sugestões de investimentos"""
        summary = financial_data["summary"]

        # Formatar investimentos existentes
        investment_text = ""
        if investment_data:
            investment_text = "\n".join([
                f"- {inv['name']} ({inv['type']}): R$ {inv['current_amount']:.2f}"
                for inv in investment_data
            ])
        else:
            investment_text = "Nenhum investimento registrado"

        # Calcular perfil de risco baseado no saldo
        monthly_balance = summary["balance"]
        risk_profile = "conservador"
        if monthly_balance > 5000:
            risk_profile = "moderado"
        if monthly_balance > 15000:
            risk_profile = "arrojado"

        prompt = f"""
        Você é um assistente financeiro especializado em análise de finanças pessoais e investimentos.
        {LANGUAGE_INSTRUCTION}

        Com base no perfil financeiro abaixo, forneça sugestões de investimentos personalizadas.

        ## Perfil Financeiro:
        - Receita total mensal: R$ {summary["total_income"]:.2f}
        - Despesas totais mensais: R$ {summary["total_expenses"]:.2f}
        - Saldo mensal: R$ {monthly_balance:.2f}
        - Total já investido: R$ {total_invested:.2f}
        - Perfil de risco estimado: {risk_profile}

        ## Investimentos Atuais:
        {investment_text}

        ## Diretrizes para Sugestões:
        1. Considere o perfil de risco baseado no saldo disponível
        2. Sugira diversificação se já houver investimentos
        3. Recomende valores específicos para investir
        4. Inclua tanto opções de renda fixa quanto variável
        5. Considere a reserva de emergência (6 meses de despesas)

        Forneça 4-5 sugestões específicas de investimentos.
        Cada sugestão deve incluir: tipo de investimento, valor sugerido, prazo e justificativa.

        IMPORTANTE: Sua resposta DEVE estar no formato JSON exatamente como mostrado abaixo:

        ```json
        {{
          "suggestions": [
            {{
              "title": "Nome do Investimento",
              "explanation": "Descrição detalhada do investimento, valor sugerido, prazo e justificativa",
              "investment_type": "tipo (renda_fixa, renda_variavel, fundos, etc)",
              "suggested_amount": valor_numerico,
              "risk_level": "baixo/medio/alto",
              "timeframe": "prazo sugerido"
            }},
            ...
          ]
        }}
        ```

        Foque em sugestões práticas e adequadas ao perfil financeiro do usuário.
        Seja específico sobre valores e prazos.
        """

        return prompt

    async def _categorize_batch(
        self,
        transactions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Categoriza um lote de transações usando IA"""
        # Preparar descrições das transações para o prompt
        transaction_descriptions = "\n".join([
            f"{i+1}. '{t['description']}' - R$ {t['amount']:.2f}"
            for i, t in enumerate(transactions)
        ])

        prompt = f"""
        Você é um assistente financeiro especializado em análise de finanças pessoais.
        {LANGUAGE_INSTRUCTION}

        Categorize as seguintes transações financeiras nas categorias apropriadas:

        {transaction_descriptions}

        Use estas categorias:
        - alimentação (mercado, restaurantes, delivery)
        - moradia (aluguel, condomínio, luz, água, gás, internet)
        - transporte (combustível, transporte público, táxi/uber)
        - saúde (farmácia, consultas, plano de saúde)
        - educação (cursos, mensalidades, materiais)
        - lazer (cinema, streaming, viagens, bares)
        - vestuário (roupas, calçados, acessórios)
        - outros (tudo que não se encaixa nas categorias acima)

        Responda no formato JSON:
        {{
          "categories": [
            {{"id": 1, "category": "categoria sugerida"}},
            ...
          ]
        }}

        Use apenas as categorias fornecidas.
        """

        try:
            # Obter resposta da IA
            ai_response = await self._query_ai(prompt)

            # Processar a resposta
            response_data = json.loads(ai_response)
            category_assignments = response_data.get("categories", [])

            # Atualizar transações com categorias sugeridas
            result = []
            for t in transactions:
                transaction_copy = t.copy()

                # Buscar categoria sugerida
                for cat_assignment in category_assignments:
                    if cat_assignment.get("id") == t.get("id"):
                        transaction_copy["suggested_category"] = cat_assignment.get("category")
                        break

                result.append(transaction_copy)

            return result

        except Exception as e:
            logger.error(f"Erro ao categorizar lote: {str(e)}")
            # Retornar transações originais em caso de erro
            return transactions

    async def _query_ai(self, prompt: str) -> str:
        """Envia uma consulta para o provedor de IA e retorna a resposta"""
        if not self.enabled:
            logger.error("Tentativa de consultar IA quando o módulo está desativado")
            raise ValueError("O módulo de IA está desativado")

        # Verificar se o serviço está disponível antes de fazer a consulta
        is_available = await self.is_available()
        if not is_available:
            logger.error(f"Serviço de IA não está disponível. Provedor: {self.provider}")
            raise ValueError(f"Serviço de IA não está disponível. Provedor: {self.provider}")

        logger.info(f"Enviando consulta para o provedor de IA: {self.provider}")
        logger.debug(f"Prompt: {prompt[:100]}...")

        try:
            response = ""
            if self.provider == AIProviderType.OLLAMA:
                response = await self._query_ollama(prompt)
            elif self.provider == AIProviderType.LM_STUDIO:
                response = await self._query_lm_studio(prompt)
            else:
                raise ValueError(f"Provedor de IA não suportado: {self.provider}")

            logger.info(f"Resposta recebida do provedor de IA: {response[:100]}...")
            return response

        except Exception as e:
            logger.error(f"Erro ao consultar IA: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _get_available_models(self):
        """Obtém a lista de modelos disponíveis no Ollama"""
        try:
            import requests
            response = requests.get(f"{settings.OLLAMA_BASE_URL}/api/tags", timeout=5.0)
            if response.status_code == 200:
                return response.json().get("models", [])
            return []
        except Exception as e:
            logger.error(f"Erro ao obter modelos disponíveis: {str(e)}")
            return []

    async def _query_ollama(self, prompt: str) -> str:
        """Consulta a API do Ollama"""
        url = f"{settings.OLLAMA_BASE_URL}/api/generate"

        # Verificar se o modelo tem o formato correto (com :latest se necessário)
        model = settings.OLLAMA_MODEL
        available_models = self._get_available_models()
        available_model_names = [m.get("name") for m in available_models]

        logger.info(f"Modelos disponíveis para consulta: {available_model_names}")

        if model == "llama3" and "llama3:latest" in available_model_names:
            model = "llama3:latest"
            logger.info(f"Ajustando modelo de 'llama3' para '{model}'")
        elif model == "mistral" and "mistral:latest" in available_model_names:
            model = "mistral:latest"
            logger.info(f"Ajustando modelo de 'mistral' para '{model}'")

        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": settings.AI_TEMPERATURE,
                "num_predict": settings.AI_MAX_TOKENS
            }
        }

        logger.info(f"Enviando requisição para Ollama: {url}")
        logger.info(f"Modelo: {settings.OLLAMA_MODEL}, Temperatura: {settings.AI_TEMPERATURE}, Max Tokens: {settings.AI_MAX_TOKENS}")

        try:
            # Verificar conexão com o Ollama antes de enviar a requisição principal
            async with httpx.AsyncClient() as client:
                try:
                    # Verificar se o serviço está respondendo
                    check_response = await client.get(
                        f"{settings.OLLAMA_BASE_URL}/api/tags",
                        timeout=5.0
                    )
                    if check_response.status_code != 200:
                        logger.error(f"Serviço Ollama não está respondendo corretamente. Status: {check_response.status_code}")
                except Exception as e:
                    logger.error(f"Erro ao verificar disponibilidade do Ollama: {str(e)}")
                    raise ValueError("Serviço Ollama não está disponível")

                # Enviar a requisição principal
                logger.info("Iniciando requisição para Ollama")
                response = await client.post(
                    url,
                    json=payload,
                    timeout=settings.AI_REQUEST_TIMEOUT
                )
                logger.info(f"Resposta recebida do Ollama: status {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"Erro na API Ollama: {response.text}")
                    raise ValueError(f"Erro no serviço de IA: {response.text}")

                result = response.json()
                response_text = result.get("response", "")
                if not response_text:
                    logger.error(f"Resposta vazia do Ollama. Resposta completa: {result}")
                    raise ValueError("Resposta vazia do serviço de IA")

                logger.info(f"Resposta do Ollama recebida com sucesso: {response_text[:100]}...")
                return response_text
        except httpx.TimeoutException as e:
            logger.error(f"Timeout ao conectar com o Ollama: {str(e)}")
            raise ValueError("Timeout ao conectar com o serviço de IA")
        except httpx.ConnectError as e:
            logger.error(f"Erro de conexão com o Ollama: {str(e)}")
            raise ValueError("Não foi possível conectar ao serviço de IA")
        except ValueError as e:
            # Repassar erros de ValueError
            logger.error(f"Erro de valor ao consultar Ollama: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao consultar Ollama: {str(e)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"Erro inesperado no serviço de IA: {str(e)}")

    async def _query_lm_studio(self, prompt: str) -> str:
        """Consulta a API do LM Studio"""
        url = f"{settings.LM_STUDIO_BASE_URL}/chat/completions"

        payload = {
            "model": settings.LM_STUDIO_MODEL,
            "messages": [
                {"role": "system", "content": f"Você é um assistente financeiro especializado em análise de finanças pessoais. {LANGUAGE_INSTRUCTION}"},
                {"role": "user", "content": prompt}
            ],
            "temperature": settings.AI_TEMPERATURE,
            "max_tokens": settings.AI_MAX_TOKENS,
            "stream": False
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                json=payload,
                timeout=settings.AI_REQUEST_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(f"Erro na API LM Studio: {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Erro no serviço de IA: {response.text}"
                )

            result = response.json()
            message = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            return message

    def _parse_ai_response(self, response: str) -> List[Dict[str, Any]]:
        """Analisa a resposta da IA e extrai informações úteis"""
        try:
            logger.info(f"Analisando resposta da IA: {response[:100]}...")

            # Tentar extrair JSON da resposta
            # Primeiro, tentar encontrar um bloco JSON
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response)

            if json_match:
                logger.info("Encontrado bloco JSON na resposta")
                json_str = json_match.group(1).strip()
            else:
                # Procurar por chaves JSON no texto
                json_match = re.search(r'(\{[\s\S]*\})', response)
                if json_match:
                    logger.info("Encontrado objeto JSON na resposta")
                    json_str = json_match.group(1).strip()
                else:
                    # Caso contrário, assumir que toda a resposta é JSON
                    logger.info("Assumindo que toda a resposta é JSON")
                    json_str = response.strip()

            # Limpar o JSON para garantir que é válido
            json_str = json_str.replace('\n', ' ').replace('\t', ' ')

            logger.info(f"Tentando analisar JSON: {json_str[:100]}...")
            data = json.loads(json_str)

            # Verificar se contém as chaves esperadas
            for key in ["insights", "recommendations"]:
                if key in data:
                    logger.info(f"Encontrada chave '{key}' no JSON")
                    return data[key]

            # Se não, retornar o primeiro array encontrado
            for key, value in data.items():
                if isinstance(value, list):
                    logger.info(f"Usando lista da chave '{key}' no JSON")
                    return value

            # Fallback - criar insights a partir do JSON
            logger.info("Criando insights a partir do JSON")
            insights = []
            for key, value in data.items():
                if isinstance(value, str):
                    insights.append({"title": key, "explanation": value})

            if insights:
                return insights

            # Último fallback - retornar resposta crua como um único item
            logger.warning("Usando fallback para resposta não estruturada")
            return [{"title": "Análise Financeira", "explanation": response}]

        except json.JSONDecodeError as e:
            logger.warning(f"Não foi possível analisar a resposta como JSON: {e}")
            # Tentar extrair informações úteis do texto
            lines = response.split('\n')
            insights = []

            current_title = None
            current_explanation = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Verificar se é um título (começa com número ou tem menos de 60 caracteres)
                if re.match(r'^\d+\.\s+', line) or (len(line) < 60 and not current_title):
                    # Se já temos um título, salvar o insight anterior
                    if current_title and current_explanation:
                        insights.append({
                            "title": current_title,
                            "explanation": ' '.join(current_explanation)
                        })

                    current_title = line
                    current_explanation = []
                elif current_title:
                    current_explanation.append(line)

            # Adicionar o último insight
            if current_title and current_explanation:
                insights.append({
                    "title": current_title,
                    "explanation": ' '.join(current_explanation)
                })

            # Se conseguimos extrair insights, retorná-los
            if insights:
                logger.info(f"Extraídos {len(insights)} insights do texto")
                return insights

            # Fallback final - retornar resposta crua como um único item
            logger.warning("Usando fallback final para resposta não estruturada")
            return [{"title": "Análise Financeira", "explanation": response}]

        except Exception as e:
            logger.error(f"Erro ao analisar resposta da IA: {str(e)}")
            logger.error(traceback.format_exc())
            return [{"title": "Erro na Análise", "explanation": "Não foi possível processar a resposta da IA. Por favor, tente novamente mais tarde."}]


# Criar instância única do assistente
finance_ai = FinanceAIAssistant()