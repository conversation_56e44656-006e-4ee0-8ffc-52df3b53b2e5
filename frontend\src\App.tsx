import { <PERSON>rowserRouter } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './contexts/AuthContext';
import { FiltersProvider } from './contexts/FiltersContext';
import Routes from './Routes';

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <FiltersProvider>
          <CssBaseline />
          <Routes />
        </FiltersProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;