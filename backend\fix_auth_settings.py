#!/usr/bin/env python3
"""
Script para substituir security_settings por settings no arquivo auth.py
"""

import re

def fix_auth_settings():
    file_path = "app/routers/auth.py"
    
    # Ler o arquivo
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituir security_settings por settings
    content = re.sub(r'security_settings\.', 'settings.', content)
    
    # Escrever o arquivo de volta
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Substituições realizadas com sucesso!")

if __name__ == "__main__":
    fix_auth_settings()
