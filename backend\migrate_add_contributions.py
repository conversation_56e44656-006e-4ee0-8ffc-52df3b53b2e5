#!/usr/bin/env python3
"""
Migração para criar a tabela contributions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_contributions():
    """Cria a tabela contributions"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # Verificar se a tabela já existe
            result = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='contributions'"))
            table_exists = result.fetchone() is not None
            
            if table_exists:
                logger.info("✅ Tabela contributions já existe")
                return True
            
            # Criar a tabela contributions
            logger.info("Criando tabela contributions...")
            connection.execute(text("""
                CREATE TABLE contributions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    amount REAL NOT NULL,
                    date DATE DEFAULT (date('now')),
                    description TEXT,
                    created_at DATETIME DEFAULT (datetime('now')),
                    updated_at DATETIME DEFAULT (datetime('now')),
                    goal_id INTEGER NOT NULL,
                    FOREIGN KEY (goal_id) REFERENCES financial_goals (id)
                )
            """))
            connection.commit()
            
            logger.info("✅ Migração concluída com sucesso!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro na migração: {str(e)}")
        return False

if __name__ == "__main__":
    success = migrate_add_contributions()
    if success:
        print("✅ Migração executada com sucesso!")
        sys.exit(0)
    else:
        print("❌ Falha na migração!")
        sys.exit(1)
