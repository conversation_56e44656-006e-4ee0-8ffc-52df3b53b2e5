#!/usr/bin/env python3
"""
Script para resetar a senha do usuário admin
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models.financial import User
from app.security import get_password_hash
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_admin_password():
    """Reseta a senha do usuário admin"""
    
    db = SessionLocal()
    
    try:
        # Buscar usuário admin
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not user:
            logger.error("Usuário <EMAIL> não encontrado")
            return False
        
        # Resetar senha para admin123
        new_password = "admin123"
        user.hashed_password = get_password_hash(new_password)
        
        db.commit()
        logger.info(f"Senha do usuário {user.email} resetada para: {new_password}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao resetar senha: {str(e)}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = reset_admin_password()
    if success:
        print("✅ Senha resetada com sucesso!")
        print("Email: <EMAIL>")
        print("Senha: admin123")
    else:
        print("❌ Falha ao resetar senha")
        sys.exit(1)
