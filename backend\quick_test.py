#!/usr/bin/env python3

import requests
import json

def test_critical_issues():
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 TESTING ALL CRITICAL ISSUES")
    print("=" * 60)
    
    try:
        # Test 1: Health check
        print("1. Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   Health: {response.status_code} ✅" if response.status_code == 200 else f"   Health: {response.status_code} ❌")
        
        # Test 2: Login (CRITICAL ISSUE 1)
        print("\n2. Testing login (CRITICAL ISSUE 1)...")
        login_data = {
            'username': '<EMAIL>',
            'password': 'admin123'
        }
        
        response = requests.post(f"{base_url}/auth/token", data=login_data, timeout=10)
        print(f"   Login: {response.status_code}", end="")
        
        if response.status_code == 200:
            print(" ✅ CRITICAL ISSUE 1 FIXED!")
            token = response.json()['access_token']
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test 3: Financial Goals (ISSUE 4)
            print("\n3. Testing financial goals (ISSUE 4)...")
            response = requests.get(f"{base_url}/financial-goals/", headers=headers, timeout=10)
            print(f"   Financial goals: {response.status_code}", end="")
            if response.status_code == 200:
                print(" ✅ ISSUE 4A FIXED!")
            else:
                print(f" ❌ Error: {response.text[:50]}")
            
            # Test 4: Budgets (ISSUE 4)
            print("\n4. Testing budgets (ISSUE 4)...")
            response = requests.get(f"{base_url}/budgets/", headers=headers, timeout=10)
            print(f"   Budgets: {response.status_code}", end="")
            if response.status_code == 200:
                print(" ✅ ISSUE 4B FIXED!")
            else:
                print(f" ❌ Error: {response.text[:50]}")
            
            # Test 5: Investments (ISSUE 2)
            print("\n5. Testing investments (ISSUE 2)...")
            response = requests.get(f"{base_url}/investments/", headers=headers, timeout=10)
            print(f"   Investments: {response.status_code}", end="")
            if response.status_code == 200:
                print(" ✅ ISSUE 2A FIXED!")
            else:
                print(f" ❌ Error: {response.text[:50]}")
            
            # Test 6: Investment Transaction Creation (ISSUE 2)
            print("\n6. Testing investment transaction creation (ISSUE 2)...")
            transaction_data = {
                'description': 'Teste Investimento',
                'amount': 1000.0,
                'type': 'investment',
                'category': 'Investimentos',
                'date': '2025-01-20',
                'subtype': 'aplicacao'
            }
            
            response = requests.post(f"{base_url}/transactions/", 
                                   json=transaction_data, 
                                   headers=headers, 
                                   timeout=10)
            print(f"   Create investment transaction: {response.status_code}", end="")
            if response.status_code == 200:
                print(" ✅ ISSUE 2B FIXED!")
            else:
                print(f" ❌ Error: {response.text[:50]}")
            
            # Test 7: AI Investment Suggestions (ISSUE 3)
            print("\n7. Testing AI investment suggestions (ISSUE 3)...")
            response = requests.get(f"{base_url}/ai/investment-suggestions", headers=headers, timeout=15)
            print(f"   AI investment suggestions: {response.status_code}", end="")
            if response.status_code == 200:
                print(" ✅ ISSUE 3 FIXED!")
            else:
                print(f" ❌ Error: {response.text[:50]}")
        
        else:
            print(f" ❌ Login failed: {response.text}")
        
        print("\n" + "=" * 60)
        print("🎯 CRITICAL ISSUES TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")

if __name__ == "__main__":
    test_critical_issues()
