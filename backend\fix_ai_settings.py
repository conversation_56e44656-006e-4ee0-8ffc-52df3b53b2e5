#!/usr/bin/env python3
"""
Script para substituir self.settings. por settings. no arquivo ai_assistance.py
"""

import re

def fix_ai_settings():
    file_path = "app/services/ai_assistance.py"
    
    # Ler o arquivo
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituir self.settings. por settings.
    content = re.sub(r'self\.settings\.', 'settings.', content)
    
    # Escrever o arquivo de volta
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Substituições realizadas com sucesso!")

if __name__ == "__main__":
    fix_ai_settings()
