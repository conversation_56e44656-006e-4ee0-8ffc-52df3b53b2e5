# Arquivo de configuração OAuth para Finance App
# 
# Este arquivo deve conter suas credenciais do Google OAuth2
# Renomeie este arquivo para 'oauth.txt' e preencha com suas credenciais reais
#
# Você pode colocar este arquivo em qualquer um dos seguintes locais:
# - Na raiz do projeto (mesmo diretório que este arquivo)
# - Na sua pasta home (~/)
# - Em ~/Documents/
# - Em C:\oauth\ (Windows) ou /etc/oauth/ (Linux/Mac)
#
# Formatos suportados:
#
# 1. Formato chave=valor (recomendado):
GOOGLE_CLIENT_ID=seu_client_id_aqui.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=sua_client_secret_aqui

# 2. Formato JSON (alternativo):
# {
#   "GOOGLE_CLIENT_ID": "seu_client_id_aqui.apps.googleusercontent.com",
#   "GOOGLE_CLIENT_SECRET": "sua_client_secret_aqui"
# }
#
# Para obter suas credenciais:
# 1. Acesse https://console.cloud.google.com/
# 2. Crie um novo projeto ou selecione um existente
# 3. Ative a API do Google+ ou Google Identity
# 4. Vá em "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
# 5. Configure as URLs de redirecionamento:
#    - http://localhost:8000/api/v1/auth/google/callback
#    - http://127.0.0.1:8000/api/v1/auth/google/callback
# 6. Copie o Client ID e Client Secret para este arquivo
#
# IMPORTANTE: 
# - Nunca compartilhe este arquivo com suas credenciais reais
# - Adicione oauth.txt ao seu .gitignore
# - Mantenha suas credenciais seguras
