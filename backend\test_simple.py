#!/usr/bin/env python3
"""
Teste simples para verificar se os módulos carregam
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("1. Testando importação de models...")
    from app.models.financial import User, Transaction, Budget
    print("✅ Models importados com sucesso")
    
    print("\n2. Testando importação de schemas...")
    from app.schemas.financial import User as UserSchema, Transaction as TransactionSchema
    print("✅ Schemas importados com sucesso")
    
    print("\n3. Testando importação de security...")
    from app.security import get_current_active_user, create_access_token
    print("✅ Security importado com sucesso")
    
    print("\n4. Testando importação de routers...")
    from app.routers import auth, financial, budgets, investments
    print("✅ Routers importados com sucesso")
    
    print("\n5. Testando importação de dashboard...")
    from app.routers import dashboard
    print("✅ Dashboard importado com sucesso")
    
    print("\n6. Testando importação do main...")
    from app.main import app
    print("✅ Main app importado com sucesso")
    
    print("\n✅ TODOS OS MÓDULOS CARREGARAM COM SUCESSO!")
    
except Exception as e:
    print(f"\n❌ Erro: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
