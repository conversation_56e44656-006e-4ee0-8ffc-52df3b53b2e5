# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.env

# Banco de dados
*.db
*.sqlite3

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
dist/
dist-ssr/
*.local

# Editor/IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs/
*.log
debug_uploads/
temp_uploads/
diagnostics/

# Coverage
coverage/
.coverage
htmlcov/

# Ambiente virtual
.env
.venv
venv/
ENV/
.conda/

# Cache
.cache/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# TypeScript
*.tsbuildinfo

# Arquivos temporários de diagnóstico
backend/diagnostics/
backend/debug_uploads/
backend/temp_uploads/

# Arquivos de sessão de diagnóstico
*_document_processing_*/
session.log
report.json
request_info.json
validation_results.json
db_errors.json
saved_transactions.json
errors.log

# Credenciais OAuth (segurança)
oauth.txt
**/oauth.txt

# Outros
.DS_Store
Thumbs.db
