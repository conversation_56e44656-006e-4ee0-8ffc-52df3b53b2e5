#!/usr/bin/env python3
"""
Teste para verificar se o login local ainda funciona na branch Google OAuth
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app

def test_local_login():
    """Testa se o login local ainda funciona"""
    
    try:
        client = TestClient(app)
        print("=== TESTE DE LOGIN LOCAL ===")
        
        # 1. Testar health
        print("\n1. Testando endpoint health...")
        response = client.get("/api/v1/health")
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Endpoint health não funciona")
            return False
        
        # 2. Testar login local
        print("\n2. Testando login local...")
        login_data = {
            "username": "<EMAIL>",
            "password": "admin123"
        }
        
        response = client.post("/api/v1/auth/token", data=login_data)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Login local falhou: {response.text}")
            return False
        
        token_data = response.json()
        token = token_data["access_token"]
        print(f"✅ Login local bem-sucedido! Token: {token[:50]}...")
        
        # 3. Testar endpoint autenticado
        print("\n3. Testando endpoint autenticado...")
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/users/me", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Endpoint autenticado falhou: {response.text}")
            return False
        
        user_data = response.json()
        print(f"✅ Usuário autenticado: {user_data['email']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_local_login()
    if success:
        print("\n✅ LOGIN LOCAL FUNCIONANDO!")
    else:
        print("\n❌ PROBLEMA NO LOGIN LOCAL")
        sys.exit(1)
