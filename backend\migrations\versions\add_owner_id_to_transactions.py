"""add_owner_id_to_transactions

Revision ID: add_owner_id_001
Revises: 9539b30d5494
Create Date: 2025-06-05 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_owner_id_001'
down_revision = '9539b30d5494'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add owner_id column to transactions table
    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('owner_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_transactions_owner_id', 'users', ['owner_id'], ['id'])

    # Set owner_id to user_id for existing transactions (backward compatibility)
    op.execute("UPDATE transactions SET owner_id = user_id WHERE owner_id IS NULL")


def downgrade() -> None:
    # Drop foreign key constraint and owner_id column
    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.drop_constraint('fk_transactions_owner_id', type_='foreignkey')
        batch_op.drop_column('owner_id')
