"""add_owner_id_to_transactions

Revision ID: add_owner_id_001
Revises: 7c84757a790d
Create Date: 2025-06-05 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_owner_id_001'
down_revision = '7c84757a790d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add owner_id column to transactions table
    op.add_column('transactions', sa.Column('owner_id', sa.Integer(), nullable=True))
    
    # Create foreign key constraint
    op.create_foreign_key(
        'fk_transactions_owner_id', 
        'transactions', 
        'users', 
        ['owner_id'], 
        ['id']
    )
    
    # Set owner_id to user_id for existing transactions (backward compatibility)
    op.execute("UPDATE transactions SET owner_id = user_id WHERE owner_id IS NULL")


def downgrade() -> None:
    # Drop foreign key constraint
    op.drop_constraint('fk_transactions_owner_id', 'transactions', type_='foreignkey')
    
    # Drop owner_id column
    op.drop_column('transactions', 'owner_id')
