import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Snackbar,
  Alert,
  Grid,
  Paper
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useFilters } from '../contexts/FiltersContext';
import GlobalFilters from '../components/GlobalFilters/GlobalFilters';
import CollapsibleNavBar from '../components/Navigation/CollapsibleNavBar';
import FinancialGoalList from '../components/Goals/FinancialGoalList';
import BudgetManager from '../components/Goals/BudgetManager';
import { AISavingsRecommendations } from '../components/AI';
import api from '../services/api';

const Planning: React.FC = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [feedback, setFeedback] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const { filters } = useFilters();

  useEffect(() => {
    let mounted = true;

    const checkAdminStatus = async () => {
      try {
        const response = await api.get('/auth/users/me');
        if (mounted && response.data) {
          setIsAdmin(response.data.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de admin:', error);
        if (mounted) {
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, []);

  const handleCloseFeedback = () => {
    setFeedback(prev => ({
      ...prev,
      open: false
    }));
  };

  const handleFeedback = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setFeedback({
      open: true,
      message,
      severity
    });
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Barra de Navegação Colapsável */}
      <CollapsibleNavBar isAdmin={isAdmin} title="Finance App - Planejamento" />

      {/* Container centralizado com 80% de largura para todo o conteúdo */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Typography variant="h4" gutterBottom>
          Planejamento Financeiro
        </Typography>

        {/* Filtros Globais */}
        <GlobalFilters
          filters={filters}
          onFiltersChange={() => {}} // Será gerenciado pelo contexto
          showFamilyFilter={true}
        />

        <Grid container spacing={3}>
          {/* Recomendações de IA */}
          <Grid item xs={12}>
            <AISavingsRecommendations
              onFeedback={handleFeedback}
            />
          </Grid>

          {/* Metas Financeiras */}
          <Grid item xs={12}>
            <Paper sx={{ p: 4, mb: 3 }}>
              <FinancialGoalList
                onFeedback={handleFeedback}
              />
            </Paper>
          </Grid>

          {/* Gerenciador de Orçamentos */}
          <Grid item xs={12}>
            <Paper sx={{ p: 4 }}>
              <BudgetManager
                onFeedback={handleFeedback}
              />
            </Paper>
          </Grid>
        </Grid>

        <Snackbar
          open={feedback.open}
          autoHideDuration={6000}
          onClose={handleCloseFeedback}
        >
          <Alert
            onClose={handleCloseFeedback}
            severity={feedback.severity}
            variant="filled"
          >
            {feedback.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Planning;
