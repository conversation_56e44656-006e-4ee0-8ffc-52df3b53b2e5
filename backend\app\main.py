from fastapi import FastAP<PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import os
import logging
import time
import traceback
from datetime import datetime

from .security import get_current_active_user
from .routers import auth, financial, document, ai, financial_goals, budgets, investments, admin, goals, family
from .models.financial import User
from .core.logging_config import setup_logging
from .database import engine, Base
from .config import settings

# Configuração de logs
setup_logging()
logging.basicConfig(
    level=logging.DEBUG,  # Alterado de INFO para DEBUG para mostrar mais detalhes
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/app-{datetime.now().strftime('%Y-%m-%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Logger para erros separados
error_logger = logging.getLogger("errors")
error_logger.setLevel(logging.DEBUG)  # Alterado para DEBUG
error_logger.addHandler(logging.FileHandler(f"logs/errors-{datetime.now().strftime('%Y-%m-%d')}.log"))

# Logger para transações
transaction_logger = logging.getLogger("transactions")
transaction_logger.setLevel(logging.INFO)
transaction_logger.addHandler(logging.FileHandler(f"logs/transactions-{datetime.now().strftime('%Y-%m-%d')}.log"))

# Logger para uploads
upload_logger = logging.getLogger("uploads")
upload_logger.setLevel(logging.DEBUG)  # Nível DEBUG para capturar todos os detalhes
upload_logger.addHandler(logging.FileHandler(f"logs/uploads-{datetime.now().strftime('%Y-%m-%d')}.log"))

app = FastAPI(
    title="Finance App API",
    description="API para gerenciamento financeiro pessoal",
    version="1.0.0"
)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # Registrar detalhes da requisição
    method = request.method
    url = str(request.url)

    # Registrar headers detalhados para requisições de upload (para diagnóstico)
    if "/documents/process" in url:
        upload_logger.debug(f"Recebendo requisição de upload: {method} {url}")
        try:
            headers = dict(request.headers)
            # Remover informações sensíveis como tokens
            if "authorization" in headers:
                headers["authorization"] = headers["authorization"][:15] + "..." if headers["authorization"] else "None"
            upload_logger.debug(f"Headers da requisição: {headers}")
        except Exception as e:
            upload_logger.error(f"Erro ao registrar headers: {str(e)}")

    logger.info(f"Recebendo requisição: {method} {url}")

    try:
        response = await call_next(request)

        process_time = time.time() - start_time
        logger.info(f"{method} {request.url.path} - {response.status_code} - {process_time:.3f}s")

        # Registrar respostas de erro em uploads
        if "/documents/process" in url and response.status_code >= 400:
            upload_logger.error(f"Erro no upload: {method} {url} - Status: {response.status_code}")
            try:
                body = await request.body()
                upload_logger.debug(f"Tamanho do corpo da requisição: {len(body)} bytes")
            except Exception as e:
                upload_logger.error(f"Erro ao acessar corpo da requisição: {str(e)}")

        return response

    except Exception as e:
        process_time = time.time() - start_time
        error_detail = traceback.format_exc()
        error_logger.error(f"Erro não tratado: {str(e)}\n{error_detail}")
        logger.error(f"Erro na requisição {method} {url}: {str(e)}")

        if "/documents/process" in url:
            upload_logger.error(f"Erro fatal no upload: {method} {url} - Erro: {str(e)}\n{error_detail}")

        return JSONResponse(
            status_code=500,
            content={"detail": "Erro interno do servidor. Por favor tente novamente."}
        )

# Criar tabelas no banco de dados
Base.metadata.create_all(bind=engine)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permitir todas as origens em desenvolvimento
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Definir pasta de arquivos estáticos
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "static")
os.makedirs(static_dir, exist_ok=True)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Incluir rotas com prefixos corretos
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(admin.router, prefix="/api/v1", tags=["admin"])
app.include_router(financial.router, prefix="/api/v1", tags=["financial"])
app.include_router(document.router, prefix="/api/v1", tags=["documents"])
app.include_router(ai.router, prefix="/api/v1", tags=["ai"])
app.include_router(financial_goals.router, prefix="/api/v1", tags=["financial-goals"])
app.include_router(budgets.router, prefix="/api/v1", tags=["budgets"])
app.include_router(investments.router, prefix="/api/v1", tags=["investments"])
app.include_router(goals.router, prefix="/api/v1", tags=["goals"])
app.include_router(family.router, prefix="/api/v1", tags=["family"])

@app.get("/api/v1/health")
async def health_check():
    return {"status": "ok", "version": app.version}

@app.get("/api/v1/auth/users/me")
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return {
        "id": current_user.id,
        "email": current_user.email,
        "first_name": current_user.first_name,
        "last_name": current_user.last_name,
        "is_active": current_user.is_active,
        "username": getattr(current_user, 'username', None),
        "is_verified": getattr(current_user, 'is_verified', None)
    }

@app.get("/")
async def root():
    return JSONResponse(
        content={
            "message": "Bem-vindo à API do Finance App",
            "docs_url": "/docs",
            "openapi_url": "/openapi.json"
        }
    )