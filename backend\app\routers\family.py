from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from ..database import get_db
from ..models.financial import User, FamilyLink
from ..schemas.financial import (
    FamilyLinkCreate, 
    FamilyLink as FamilyLinkSchema, 
    FamilyLinkUpdate
)
from ..security import get_current_active_user
import logging

router = APIRouter(
    prefix="/family",
    tags=["family"],
)

logger = logging.getLogger(__name__)


@router.post("/invite", response_model=FamilyLinkSchema)
async def send_family_invite(
    invite: FamilyLinkCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Envia um convite para vincular contas familiares
    """
    # Verificar se o usuário convidado existe
    invited_user = db.query(User).filter(User.email == invite.invited_email).first()
    if not invited_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado com este email"
        )
    
    # Verificar se não está tentando convidar a si mesmo
    if invited_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Você não pode convidar a si mesmo"
        )
    
    # Verificar se já existe um convite pendente ou aceito
    existing_link = db.query(FamilyLink).filter(
        ((FamilyLink.requester_id == current_user.id) & (FamilyLink.invited_id == invited_user.id)) |
        ((FamilyLink.requester_id == invited_user.id) & (FamilyLink.invited_id == current_user.id))
    ).first()
    
    if existing_link:
        if existing_link.status == "accepted":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vocês já estão vinculados como família"
            )
        elif existing_link.status == "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Já existe um convite pendente entre vocês"
            )
    
    # Criar novo convite
    family_link = FamilyLink(
        requester_id=current_user.id,
        invited_id=invited_user.id,
        status="pending"
    )
    
    db.add(family_link)
    db.commit()
    db.refresh(family_link)
    
    # Adicionar informações dos usuários para retorno
    result = FamilyLinkSchema(
        id=family_link.id,
        requester_id=family_link.requester_id,
        invited_id=family_link.invited_id,
        status=family_link.status,
        created_at=family_link.created_at,
        updated_at=family_link.updated_at,
        requester_name=f"{current_user.first_name} {current_user.last_name}".strip(),
        requester_email=current_user.email,
        invited_name=f"{invited_user.first_name} {invited_user.last_name}".strip(),
        invited_email=invited_user.email
    )
    
    logger.info(f"Convite familiar enviado de {current_user.email} para {invited_user.email}")
    return result


@router.get("/invites", response_model=List[FamilyLinkSchema])
async def get_family_invites(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retorna todos os convites familiares (enviados e recebidos)
    """
    # Buscar convites enviados e recebidos
    invites = db.query(FamilyLink).filter(
        (FamilyLink.requester_id == current_user.id) | 
        (FamilyLink.invited_id == current_user.id)
    ).all()
    
    result = []
    for invite in invites:
        # Buscar informações dos usuários
        requester = db.query(User).filter(User.id == invite.requester_id).first()
        invited = db.query(User).filter(User.id == invite.invited_id).first()
        
        result.append(FamilyLinkSchema(
            id=invite.id,
            requester_id=invite.requester_id,
            invited_id=invite.invited_id,
            status=invite.status,
            created_at=invite.created_at,
            updated_at=invite.updated_at,
            requester_name=f"{requester.first_name} {requester.last_name}".strip() if requester else "",
            requester_email=requester.email if requester else "",
            invited_name=f"{invited.first_name} {invited.last_name}".strip() if invited else "",
            invited_email=invited.email if invited else ""
        ))
    
    return result


@router.put("/invites/{invite_id}", response_model=FamilyLinkSchema)
async def respond_to_invite(
    invite_id: int,
    response: FamilyLinkUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Responde a um convite familiar (aceitar ou rejeitar)
    """
    # Buscar o convite
    invite = db.query(FamilyLink).filter(FamilyLink.id == invite_id).first()
    if not invite:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Convite não encontrado"
        )
    
    # Verificar se o usuário atual é o convidado
    if invite.invited_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Você só pode responder a convites enviados para você"
        )
    
    # Verificar se o convite ainda está pendente
    if invite.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Este convite já foi respondido"
        )
    
    # Validar resposta
    if response.status not in ["accepted", "rejected"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status deve ser 'accepted' ou 'rejected'"
        )
    
    # Atualizar status
    invite.status = response.status
    invite.updated_at = datetime.now()
    
    db.commit()
    db.refresh(invite)
    
    # Buscar informações dos usuários para retorno
    requester = db.query(User).filter(User.id == invite.requester_id).first()
    invited = db.query(User).filter(User.id == invite.invited_id).first()
    
    result = FamilyLinkSchema(
        id=invite.id,
        requester_id=invite.requester_id,
        invited_id=invite.invited_id,
        status=invite.status,
        created_at=invite.created_at,
        updated_at=invite.updated_at,
        requester_name=f"{requester.first_name} {requester.last_name}".strip() if requester else "",
        requester_email=requester.email if requester else "",
        invited_name=f"{invited.first_name} {invited.last_name}".strip() if invited else "",
        invited_email=invited.email if invited else ""
    )
    
    action = "aceito" if response.status == "accepted" else "rejeitado"
    logger.info(f"Convite familiar {action} por {current_user.email}")
    
    return result


@router.delete("/links/{link_id}")
async def remove_family_link(
    link_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Remove uma vinculação familiar
    """
    # Buscar a vinculação
    link = db.query(FamilyLink).filter(FamilyLink.id == link_id).first()
    if not link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vinculação não encontrada"
        )
    
    # Verificar se o usuário atual faz parte da vinculação
    if link.requester_id != current_user.id and link.invited_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Você só pode remover vinculações das quais faz parte"
        )
    
    db.delete(link)
    db.commit()
    
    logger.info(f"Vinculação familiar removida por {current_user.email}")
    return {"message": "Vinculação familiar removida com sucesso"}


@router.get("/members", response_model=List[dict])
async def get_family_members(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retorna todos os membros da família vinculados
    """
    # Buscar vinculações aceitas
    accepted_links = db.query(FamilyLink).filter(
        ((FamilyLink.requester_id == current_user.id) | (FamilyLink.invited_id == current_user.id)) &
        (FamilyLink.status == "accepted")
    ).all()
    
    family_members = []
    
    # Adicionar o próprio usuário
    family_members.append({
        "id": current_user.id,
        "name": f"{current_user.first_name} {current_user.last_name}".strip(),
        "email": current_user.email,
        "is_self": True
    })
    
    # Adicionar membros vinculados
    for link in accepted_links:
        if link.requester_id == current_user.id:
            # O usuário atual é quem enviou o convite
            member = db.query(User).filter(User.id == link.invited_id).first()
        else:
            # O usuário atual foi convidado
            member = db.query(User).filter(User.id == link.requester_id).first()
        
        if member:
            family_members.append({
                "id": member.id,
                "name": f"{member.first_name} {member.last_name}".strip(),
                "email": member.email,
                "is_self": False
            })
    
    return family_members
