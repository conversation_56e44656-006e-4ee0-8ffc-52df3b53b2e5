#!/usr/bin/env python3
"""
Script para testar endpoints básicos sem módulo AI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import httpx
import json
from fastapi import FastAPI
from fastapi.testclient import TestClient

# Importar apenas os módulos necessários
from app.main import app

def test_basic_endpoints():
    """Testa os endpoints básicos usando TestClient"""

    try:
        client = TestClient(app)
    except Exception as e:
        print(f"❌ Erro ao criar TestClient: {str(e)}")
        return

    print("=== TESTE DE ENDPOINTS BÁSICOS ===")

    # 1. Testar health
    print("\n1. Testando endpoint health...")
    response = client.get("/api/v1/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")

    # 2. Fazer login
    print("\n2. Testando login...")
    login_data = {
        "username": "<EMAIL>",
        "password": "admin123"
    }

    response = client.post("/api/v1/auth/token", data=login_data)
    print(f"Status: {response.status_code}")

    if response.status_code != 200:
        print(f"❌ Login falhou: {response.text}")
        return

    token_data = response.json()
    token = token_data["access_token"]
    print(f"✅ Login bem-sucedido! Token: {token[:50]}...")

    headers = {"Authorization": f"Bearer {token}"}

    # 3. Testar endpoint /me
    print("\n3. Testando endpoint /me...")
    response = client.get("/api/v1/auth/users/me", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

    # 4. Testar budgets
    print("\n4. Testando endpoint budgets...")
    response = client.get("/api/v1/budgets/", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

    # 5. Testar dashboard/summary
    print("\n5. Testando endpoint dashboard/summary...")
    response = client.get("/api/v1/dashboard/summary", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

if __name__ == "__main__":
    try:
        test_basic_endpoints()
        print("\n✅ Teste concluído!")
    except Exception as e:
        print(f"\n❌ Erro no teste: {str(e)}")
        import traceback
        traceback.print_exc()
