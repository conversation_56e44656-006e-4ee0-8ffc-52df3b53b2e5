<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Google OAuth - Finance App</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .user-info img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            vertical-align: middle;
        }
        .logout-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        #google-signin-btn {
            margin: 20px auto;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Demo Google OAuth2 - Finance App</h1>
        
        <div id="status" class="status info">
            Verificando configuração do Google OAuth2...
        </div>

        <div id="google-signin-btn"></div>
        
        <div id="user-info" class="user-info" style="display: none;">
            <h3>Usuário Autenticado:</h3>
            <div id="user-details"></div>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let currentUser = null;
        let accessToken = null;

        // Verificar status do Google OAuth2
        async function checkGoogleOAuthStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/google/status`);
                const data = await response.json();
                
                const statusDiv = document.getElementById('status');
                if (data.enabled) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = 'Google OAuth2 configurado e disponível';
                    initializeGoogleSignIn();
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = 'Google OAuth2 não está configurado no servidor';
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status error';
                statusDiv.textContent = 'Erro ao conectar com o servidor';
            }
        }

        // Inicializar Google Sign-In
        function initializeGoogleSignIn() {
            google.accounts.id.initialize({
                client_id: 'YOUR_GOOGLE_CLIENT_ID', // Substitua pelo seu Client ID
                callback: handleCredentialResponse
            });

            google.accounts.id.renderButton(
                document.getElementById('google-signin-btn'),
                {
                    theme: 'outline',
                    size: 'large',
                    text: 'signin_with',
                    locale: 'pt-BR'
                }
            );
        }

        // Callback do Google Sign-In
        async function handleCredentialResponse(response) {
            try {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status info';
                statusDiv.textContent = 'Autenticando com o servidor...';

                // Enviar token para o backend
                const authResponse = await fetch(`${API_BASE_URL}/auth/google/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: response.credential
                    })
                });

                if (authResponse.ok) {
                    const authData = await authResponse.json();
                    accessToken = authData.access_token;
                    currentUser = authData.user;
                    
                    showUserInfo(authData.user);
                    statusDiv.className = 'status success';
                    statusDiv.textContent = 'Login realizado com sucesso!';
                    
                    // Esconder botão de login
                    document.getElementById('google-signin-btn').style.display = 'none';
                } else {
                    const errorData = await authResponse.json();
                    throw new Error(errorData.detail || 'Erro na autenticação');
                }
            } catch (error) {
                console.error('Erro na autenticação:', error);
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status error';
                statusDiv.textContent = `Erro na autenticação: ${error.message}`;
            }
        }

        // Mostrar informações do usuário
        function showUserInfo(user) {
            const userInfoDiv = document.getElementById('user-info');
            const userDetailsDiv = document.getElementById('user-details');
            
            userDetailsDiv.innerHTML = `
                ${user.profile_picture ? `<img src="${user.profile_picture}" alt="Foto do perfil">` : ''}
                <strong>Nome:</strong> ${user.full_name || 'N/A'}<br>
                <strong>Email:</strong> ${user.email}<br>
                <strong>ID:</strong> ${user.id}<br>
                <strong>Verificado:</strong> ${user.is_verified ? 'Sim' : 'Não'}
            `;
            
            userInfoDiv.style.display = 'block';
        }

        // Logout
        function logout() {
            google.accounts.id.disableAutoSelect();
            currentUser = null;
            accessToken = null;
            
            document.getElementById('user-info').style.display = 'none';
            document.getElementById('google-signin-btn').style.display = 'block';
            
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Logout realizado. Você pode fazer login novamente.';
        }

        // Inicializar quando a página carregar
        window.onload = function() {
            checkGoogleOAuthStatus();
        };
    </script>
</body>
</html>
