# 🔐 GUIA DE LOGIN - Finance App

## 📋 **CREDENCIAIS DE ACESSO**

### 👤 **Login Local (Todas as Branches)**
```
📧 Email: <EMAIL>
🔑 Senha: admin123
```

### 🌐 **Login Google OAuth (Branch: feature-google-oauth)**
- **Status**: Implementado mas requer configuração
- **Configuração necessária**: Credenciais Google Cloud Console
- **Fallback**: Login local sempre disponível

---

## 🚀 **COMO FAZER LOGIN**

### **1. Acesso via Interface Web**
1. Abra o navegador em: `http://localhost:3000`
2. Na tela de login, insira:
   - **Email**: `<EMAIL>`
   - **Senha**: `admin123`
3. Clique em "Entrar"

### **2. Acesso via API (Desenvolvimento)**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=admin123"
```

---

## 🔧 **RESOLUÇÃO DE PROBLEMAS**

### ❌ **Erro 404 - Endpoint não encontrado**
**Problema**: `POST /api/v1/token - 404 Not Found`
**Solução**: ✅ **CORRIGIDO** - Endpoint correto é `/api/v1/auth/token`

### ❌ **Erro 401 - Não autorizado**
**Problema**: Credenciais inválidas
**Soluções**:
1. Verificar email: `<EMAIL>` (exato)
2. Verificar senha: `admin123` (exato)
3. Executar script de reset: `python ensure_admin_user.py`

### ❌ **Erro 500 - Erro interno**
**Problema**: Problemas de banco de dados ou configuração
**Soluções**:
1. Executar migração: `python migrate_user_table.py`
2. Recriar usuário: `python ensure_admin_user.py`
3. Verificar logs do servidor

---

## 🌟 **FUNCIONALIDADES POR BRANCH**

### **Branch: fix-ai-limits**
- ✅ Login local funcionando
- ✅ IA de limites de gastos ativa
- ✅ Todos os endpoints funcionando

### **Branch: fix-budgets-loading**
- ✅ Login local funcionando
- ✅ Problemas de autorização resolvidos
- ✅ Dashboard, orçamentos e metas funcionando

### **Branch: feature-google-oauth**
- ✅ Login local funcionando
- ✅ Google OAuth implementado (requer configuração)
- ✅ Sistema dual pronto

---

## 🔑 **CONFIGURAÇÃO GOOGLE OAUTH (Opcional)**

### **1. Google Cloud Console**
1. Acesse: https://console.cloud.google.com
2. Crie um projeto ou selecione existente
3. Ative a API "Google+ API"
4. Crie credenciais OAuth 2.0

### **2. Configurar Variáveis de Ambiente**
```bash
# Adicionar ao arquivo .env
GOOGLE_CLIENT_ID=seu_client_id_aqui
GOOGLE_CLIENT_SECRET=seu_client_secret_aqui
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

### **3. Testar Google OAuth**
```bash
# Verificar status
curl http://localhost:8000/api/v1/auth/google/status

# Obter URL de autorização
curl http://localhost:8000/api/v1/auth/google/login
```

---

## 🛠️ **SCRIPTS ÚTEIS**

### **Garantir usuário admin existe**
```bash
cd backend
python ensure_admin_user.py
```

### **Migrar banco para Google OAuth**
```bash
cd backend
python migrate_user_table.py
```

### **Testar endpoints**
```bash
cd backend
python test_basic_endpoints.py
```

### **Reset senha admin**
```bash
cd backend
python reset_admin_password.py
```

---

## 📊 **STATUS ATUAL**

### ✅ **FUNCIONANDO**
- Login local em todas as branches
- Sistema de autenticação completo
- Todos os endpoints principais
- IA de limites de gastos
- Dashboard financeiro

### 🔄 **PENDENTE**
- Configuração credenciais Google (opcional)
- Interface de usuário para família
- Mais funcionalidades de IA

---

## 🆘 **SUPORTE**

Se ainda tiver problemas:

1. **Verificar logs do servidor**
2. **Executar scripts de diagnóstico**
3. **Verificar se o banco de dados existe**
4. **Confirmar que está na branch correta**

**Lembre-se**: O login local sempre funciona com `<EMAIL>` / `admin123`
