from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import func, extract
import calendar

from ..database import get_db
from ..models.financial import Budget, User, Transaction
from ..schemas.financial import (
    BudgetCreate, 
    Budget as BudgetSchema,
    BudgetUpdate
)
from ..dependencies import get_current_user, get_current_active_user

router = APIRouter(
    prefix="/budgets",
    tags=["budgets"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=BudgetSchema)
async def create_budget(
    budget: BudgetCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Cria um novo orçamento para o usuário.
    """
    # Verificar se já existe um orçamento para esta categoria
    existing_budget = (
        db.query(Budget)
        .filter(
            Budget.user_id == current_user.id,
            Budget.category == budget.category,
            Budget.end_date.is_(None)  # Orçamentos ativos (sem data de término)
        )
        .first()
    )
    
    if existing_budget:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Já existe um orçamento ativo para esta categoria"
        )
    
    db_budget = Budget(
        category=budget.category,
        monthly_limit=budget.monthly_limit,
        start_date=budget.start_date if budget.start_date else datetime.now(),
        end_date=budget.end_date,
        user_id=current_user.id
    )
    
    try:
        db.add(db_budget)
        db.commit()
        db.refresh(db_budget)
        return db_budget
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao criar orçamento: {str(e)}"
        )

@router.get("/", response_model=List[BudgetSchema])
async def read_budgets(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retorna os orçamentos do usuário.
    """
    budgets = (
        db.query(Budget)
        .filter(Budget.user_id == current_user.id)
        .filter(
            # Mostrar orçamentos sem data de término ou com data de término futura
            (Budget.end_date.is_(None)) | (Budget.end_date >= datetime.now())
        )
        .order_by(Budget.category)
        .offset(skip)
        .limit(limit)
        .all()
    )
    return budgets

@router.get("/{budget_id}", response_model=BudgetSchema)
async def read_budget(
    budget_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retorna um orçamento específico do usuário.
    """
    budget = (
        db.query(Budget)
        .filter(Budget.id == budget_id, Budget.user_id == current_user.id)
        .first()
    )
    if not budget:
        raise HTTPException(status_code=404, detail="Orçamento não encontrado")
    return budget

@router.put("/{budget_id}", response_model=BudgetSchema)
async def update_budget(
    budget_id: int,
    budget_data: BudgetUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Atualiza um orçamento existente.
    """
    db_budget = (
        db.query(Budget)
        .filter(Budget.id == budget_id, Budget.user_id == current_user.id)
        .first()
    )
    
    if not db_budget:
        raise HTTPException(status_code=404, detail="Orçamento não encontrado")
    
    # Se estiver mudando a categoria, verificar se já existe orçamento para a nova categoria
    if budget_data.category and budget_data.category != db_budget.category:
        existing_budget = (
            db.query(Budget)
            .filter(
                Budget.user_id == current_user.id,
                Budget.category == budget_data.category,
                Budget.end_date.is_(None),  # Orçamentos ativos (sem data de término)
                Budget.id != budget_id  # Excluir o próprio orçamento da verificação
            )
            .first()
        )
        
        if existing_budget:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Já existe um orçamento ativo para esta categoria"
            )
    
    # Atualiza os campos
    for key, value in budget_data.dict(exclude_unset=True).items():
        setattr(db_budget, key, value)
    
    try:
        db.commit()
        db.refresh(db_budget)
        return db_budget
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao atualizar orçamento: {str(e)}"
        )

@router.delete("/{budget_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_budget(
    budget_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Exclui um orçamento.
    """
    db_budget = (
        db.query(Budget)
        .filter(Budget.id == budget_id, Budget.user_id == current_user.id)
        .first()
    )
    
    if not db_budget:
        raise HTTPException(status_code=404, detail="Orçamento não encontrado")
    
    try:
        db.delete(db_budget)
        db.commit()
        return None
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao excluir orçamento: {str(e)}"
        )

@router.get("/category/{category}", response_model=Dict[str, Any])
async def get_budget_status_by_category(
    category: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retorna o status do orçamento para uma categoria específica,
    incluindo gastos atuais e limites.
    """
    # Buscar o orçamento da categoria
    budget = (
        db.query(Budget)
        .filter(
            Budget.user_id == current_user.id,
            Budget.category == category,
            (Budget.end_date.is_(None)) | (Budget.end_date >= datetime.now())
        )
        .first()
    )
    
    if not budget:
        raise HTTPException(status_code=404, detail="Orçamento não encontrado para esta categoria")
    
    # Buscar os gastos do mês atual para esta categoria
    now = datetime.now()
    first_day_of_month = datetime(now.year, now.month, 1)
    last_day_of_month = datetime(
        now.year if now.month < 12 else now.year + 1,
        now.month + 1 if now.month < 12 else 1,
        1
    )
    
    current_spending = (
        db.query(Transaction)
        .filter(
            Transaction.user_id == current_user.id,
            Transaction.category == category,
            Transaction.type == 'expense',
            Transaction.date >= first_day_of_month,
            Transaction.date < last_day_of_month
        )
        .all()
    )
    
    total_spent = sum(transaction.amount for transaction in current_spending)
    remaining = budget.monthly_limit - total_spent
    percentage = (total_spent / budget.monthly_limit * 100) if budget.monthly_limit > 0 else 0
    
    return {
        "budget": {
            "id": budget.id,
            "category": budget.category,
            "monthly_limit": budget.monthly_limit
        },
        "current_spending": total_spent,
        "remaining": remaining,
        "percentage": percentage,
        "transactions_count": len(current_spending)
    }