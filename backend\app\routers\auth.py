from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional
from ..database import get_db
from ..models.financial import User
from ..schemas.financial import User as UserSchema
from ..security import (
    verify_password,
    create_access_token,
    security_settings,
    get_current_active_user
)
from ..services.google_oauth import google_oauth_service

router = APIRouter()

@router.post("/token", summary="Criar token de acesso")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.email == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email ou senha incorretos",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=security_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", response_model=UserSchema)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Retorna o usuário atualmente autenticado"""
    return current_user

# Endpoints do Google OAuth2
@router.get("/google/login", summary="Iniciar login com Google")
async def google_login():
    """
    Inicia o processo de login com Google OAuth2
    Retorna a URL para redirecionamento
    """
    if not google_oauth_service.is_enabled():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Google OAuth2 não está configurado"
        )

    authorization_url = google_oauth_service.get_authorization_url()
    return {"authorization_url": authorization_url}

@router.get("/google/status", summary="Status do Google OAuth2")
async def google_oauth_status():
    """Verifica se o Google OAuth2 está habilitado"""
    return {
        "enabled": google_oauth_service.is_enabled(),
        "message": "Google OAuth2 configurado" if google_oauth_service.is_enabled() else "Google OAuth2 não configurado"
    }

@router.post("/google/token", summary="Autenticar com token do Google")
async def google_token_login(
    token: str,
    db: Session = Depends(get_db)
):
    """
    Autentica o usuário usando um token ID do Google

    Args:
        token: Token ID recebido do Google
        db: Sessão do banco de dados

    Returns:
        Token de acesso e informações do usuário
    """
    if not google_oauth_service.is_enabled():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Google OAuth2 não está configurado"
        )

    # Verificar o token do Google
    google_user_info = await google_oauth_service.verify_token(token)

    # Autenticar ou criar usuário
    result = await google_oauth_service.authenticate_or_create_user(google_user_info, db)

    return result