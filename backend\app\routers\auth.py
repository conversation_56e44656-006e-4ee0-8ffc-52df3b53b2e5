from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import httpx
import secrets
from ..database import get_db
from ..models.financial import User
from ..schemas.financial import User as UserSchema, UserCreate
from ..security import (
    verify_password,
    create_access_token,
    get_current_active_user,
    get_password_hash,
    ACCESS_TOKEN_EXPIRE_MINUTES
)
from ..config import settings

router = APIRouter()

@router.post("/token", summary="Criar token de acesso")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.email == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email ou senha incorretos",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", response_model=UserSchema)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Retorna o usuário atualmente autenticado"""
    return current_user

# ENDPOINTS DE GERENCIAMENTO DE USUÁRIOS

@router.post("/users", response_model=UserSchema, summary="Criar novo usuário")
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Cria um novo usuário (apenas admin pode criar)"""

    # Verificar se o usuário atual é admin
    if current_user.email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem criar usuários"
        )

    # Verificar se o email já existe
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email já está registrado"
        )

    # Criar novo usuário
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=True
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return db_user

@router.get("/users", response_model=List[UserSchema], summary="Listar usuários")
async def list_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = 0,
    limit: int = 100
):
    """Lista todos os usuários (apenas admin)"""

    # Verificar se o usuário atual é admin
    if current_user.email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem listar usuários"
        )

    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.put("/users/{user_id}/activate", response_model=UserSchema, summary="Ativar/Desativar usuário")
async def toggle_user_activation(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Ativa ou desativa um usuário (apenas admin)"""

    # Verificar se o usuário atual é admin
    if current_user.email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem ativar/desativar usuários"
        )

    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )

    # Não permitir desativar o próprio admin
    if user.email == "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Não é possível desativar o usuário administrador"
        )

    # Alternar status
    user.is_active = not user.is_active
    db.commit()
    db.refresh(user)

    return user

@router.delete("/users/{user_id}", summary="Deletar usuário")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Deleta um usuário (apenas admin)"""

    # Verificar se o usuário atual é admin
    if current_user.email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem deletar usuários"
        )

    # Buscar usuário
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )

    # Não permitir deletar o admin
    if user.email == "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Não é possível deletar o usuário administrador"
        )

    db.delete(user)
    db.commit()

    return {"message": "Usuário deletado com sucesso"}

# ENDPOINTS GOOGLE OAUTH

@router.get("/google/status", summary="Status do Google OAuth")
async def google_oauth_status():
    """Verifica se o Google OAuth está configurado"""

    google_configured = bool(
        settings.GOOGLE_CLIENT_ID and
        settings.GOOGLE_CLIENT_SECRET
    )

    return {
        "google_oauth_enabled": google_configured,
        "client_id_configured": bool(settings.GOOGLE_CLIENT_ID),
        "client_secret_configured": bool(settings.GOOGLE_CLIENT_SECRET),
        "redirect_uri": settings.GOOGLE_REDIRECT_URI
    }

@router.get("/google/login", summary="Iniciar login com Google")
async def google_login():
    """Retorna a URL para iniciar o login com Google"""

    if not settings.GOOGLE_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Google OAuth não está configurado. Configure GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET."
        )

    # Gerar state para segurança
    state = secrets.token_urlsafe(32)

    # URL de autorização do Google
    google_auth_url = (
        "https://accounts.google.com/o/oauth2/auth?"
        f"client_id={settings.GOOGLE_CLIENT_ID}&"
        f"redirect_uri={settings.GOOGLE_REDIRECT_URI}&"
        "scope=openid email profile&"
        "response_type=code&"
        "access_type=offline&"
        f"state={state}"
    )

    return {
        "auth_url": google_auth_url,
        "state": state,
        "message": "Acesse a URL para fazer login com Google"
    }

@router.get("/google/callback", summary="Callback do Google OAuth")
async def google_callback(
    code: str,
    state: str,
    db: Session = Depends(get_db)
):
    """Processa o callback do Google OAuth"""

    if not settings.GOOGLE_CLIENT_ID or not settings.GOOGLE_CLIENT_SECRET:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Google OAuth não está configurado"
        )

    try:
        # Trocar código por token
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://oauth2.googleapis.com/token",
                data={
                    "client_id": settings.GOOGLE_CLIENT_ID,
                    "client_secret": settings.GOOGLE_CLIENT_SECRET,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": settings.GOOGLE_REDIRECT_URI,
                }
            )

            if token_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Erro ao obter token do Google"
                )

            token_data = token_response.json()
            access_token = token_data.get("access_token")

            # Obter informações do usuário
            user_response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"}
            )

            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Erro ao obter dados do usuário do Google"
                )

            user_data = user_response.json()

            # Verificar se usuário já existe
            existing_user = db.query(User).filter(
                (User.email == user_data["email"]) |
                (User.google_id == user_data["id"])
            ).first()

            if existing_user:
                # Atualizar dados do Google se necessário
                if not existing_user.google_id:
                    existing_user.google_id = user_data["id"]
                    existing_user.profile_picture = user_data.get("picture")
                    existing_user.is_verified = True
                    db.commit()

                user = existing_user
            else:
                # Criar novo usuário
                user = User(
                    email=user_data["email"],
                    google_id=user_data["id"],
                    first_name=user_data.get("given_name"),
                    last_name=user_data.get("family_name"),
                    full_name=user_data.get("name"),
                    profile_picture=user_data.get("picture"),
                    is_active=True,
                    is_verified=True,
                    hashed_password=""  # Usuários do Google não precisam de senha local
                )

                db.add(user)
                db.commit()
                db.refresh(user)

            # Criar token de acesso
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            jwt_token = create_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )

            # Redirecionar para o frontend com o token
            frontend_url = "http://localhost:3000"  # URL do frontend React
            redirect_url = f"{frontend_url}/auth/callback?token={jwt_token}&user_id={user.id}"

            return RedirectResponse(url=redirect_url)

    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro de conexão com Google: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )

@router.post("/google/link", summary="Vincular conta Google a usuário existente")
async def link_google_account(
    google_token: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Vincula uma conta Google a um usuário local existente"""

    try:
        # Verificar token do Google
        async with httpx.AsyncClient() as client:
            user_response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {google_token}"}
            )

            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Token do Google inválido"
                )

            google_data = user_response.json()

            # Verificar se o Google ID já está vinculado a outro usuário
            existing_google_user = db.query(User).filter(
                User.google_id == google_data["id"],
                User.id != current_user.id
            ).first()

            if existing_google_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Esta conta Google já está vinculada a outro usuário"
                )

            # Vincular conta Google ao usuário atual
            current_user.google_id = google_data["id"]
            current_user.profile_picture = google_data.get("picture")
            current_user.is_verified = True

            # Atualizar nome se não estiver definido
            if not current_user.first_name and google_data.get("given_name"):
                current_user.first_name = google_data.get("given_name")
            if not current_user.last_name and google_data.get("family_name"):
                current_user.last_name = google_data.get("family_name")
            if not current_user.full_name and google_data.get("name"):
                current_user.full_name = google_data.get("name")

            db.commit()
            db.refresh(current_user)

            return {
                "message": "Conta Google vinculada com sucesso",
                "user": current_user
            }

    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro de conexão com Google: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )