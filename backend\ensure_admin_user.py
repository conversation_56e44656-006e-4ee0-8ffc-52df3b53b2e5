#!/usr/bin/env python3
"""
Script para garantir que o usuário admin existe
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models.financial import User
from app.security import get_password_hash

def ensure_admin_user():
    """Garante que o usuário admin existe com as credenciais corretas"""
    
    # Criar as tabelas se não existirem
    from app.models import financial
    financial.Base.metadata.create_all(bind=engine)
    
    db: Session = SessionLocal()
    
    try:
        # Verificar se o usuário admin já existe
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if admin_user:
            print("✅ Usuário admin já existe!")
            print(f"Email: {admin_user.email}")
            print(f"Ativo: {admin_user.is_active}")
            
            # Atualizar senha para garantir que está correta
            admin_user.hashed_password = get_password_hash("admin123")
            if hasattr(admin_user, 'is_verified'):
                admin_user.is_verified = True
            db.commit()
            print("✅ Senha do admin atualizada para: admin123")
            return True
        
        # Criar novo usuário admin
        print("Criando novo usuário admin...")
        hashed_password = get_password_hash("admin123")
        
        user_data = {
            "email": "<EMAIL>",
            "hashed_password": hashed_password,
            "first_name": "Admin",
            "last_name": "User",
            "is_active": True
        }
        
        # Adicionar campos específicos para Google OAuth se existirem
        if hasattr(User, 'is_verified'):
            user_data["is_verified"] = True
        if hasattr(User, 'username'):
            user_data["username"] = "admin"
        
        admin_user = User(**user_data)
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ Usuário admin criado com sucesso!")
        print(f"Email: {admin_user.email}")
        print(f"Senha: admin123")
        print(f"ID: {admin_user.id}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar/atualizar usuário admin: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = ensure_admin_user()
    if success:
        print("\n🎉 USUÁRIO ADMIN PRONTO PARA USO!")
        print("📧 Email: <EMAIL>")
        print("🔑 Senha: admin123")
    else:
        print("\n❌ FALHA AO CONFIGURAR USUÁRIO ADMIN")
        sys.exit(1)
