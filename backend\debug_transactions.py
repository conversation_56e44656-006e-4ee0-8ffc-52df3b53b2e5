#!/usr/bin/env python3

import sys
import os
from datetime import datetime, timedelta

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models.financial import User, Transaction

def debug_transactions():
    print("🔍 DEBUGGING TRANSACTIONS")
    print("=" * 40)
    
    try:
        # Get database session
        db = next(get_db())
        
        # Get admin user
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            print("❌ Admin user not found")
            return
        
        print(f"✅ Found admin user: {admin_user.email} (ID: {admin_user.id})")
        
        # Check all transactions
        all_transactions = db.query(Transaction).filter(Transaction.user_id == admin_user.id).all()
        print(f"📊 Total transactions in DB: {len(all_transactions)}")
        
        if all_transactions:
            # Show date range
            dates = [t.date for t in all_transactions]
            min_date = min(dates)
            max_date = max(dates)
            print(f"📅 Date range: {min_date} to {max_date}")
            
            # Show transaction types
            types = {}
            for t in all_transactions:
                types[t.type] = types.get(t.type, 0) + 1
            print(f"📈 Transaction types: {types}")
        
        # Test quarter filter (last 90 days)
        now = datetime.now()
        start_date = now - timedelta(days=90)
        print(f"\n🔍 Testing quarter filter (last 90 days)")
        print(f"   Start date: {start_date}")
        print(f"   Current date: {now}")
        
        quarter_transactions = db.query(Transaction).filter(
            Transaction.user_id == admin_user.id,
            Transaction.date >= start_date
        ).all()
        
        print(f"📊 Transactions in last 90 days: {len(quarter_transactions)}")
        
        if quarter_transactions:
            # Show expense transactions only
            expense_transactions = [t for t in quarter_transactions if t.type == "expense"]
            print(f"💸 Expense transactions in last 90 days: {len(expense_transactions)}")
            
            if expense_transactions:
                print("   Sample expense transactions:")
                for t in expense_transactions[:5]:
                    print(f"     - {t.description}: R$ {t.amount} ({t.category}) - {t.date}")
        
        print("\n" + "=" * 40)
        print("🎯 TRANSACTION DEBUG COMPLETED!")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_transactions()
