import { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  InputAdornment,
  SelectChangeEvent,
  FormHelperText,
  Autocomplete,
  Box,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import ptBR from 'date-fns/locale/pt-BR/index.js';
import axios from 'axios';
import { AICategorization } from '../AI';

interface TransactionFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: TransactionFormData) => Promise<void>;
  transaction?: Transaction;
}

interface TransactionFormData {
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: Date;
  subtype: 'purchase' | 'refund' | 'payment' | 'transfer_in' | 'transfer_out' | 'investment_deposit' | 'investment_withdrawal' | 'credit_card_payment' | 'credit_card_purchase' | 'tithe' | 'donation' | 'other';
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  owner_id?: number | null;
}

interface Transaction {
  id: number;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: string;
  subtype: string;
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  owner_id?: number | null;
}

interface CreditCard {
  id: number;
  name: string;
  last_digits?: string;
}

interface Investment {
  id: number;
  name: string;
  type: string;
}

interface FamilyMember {
  id: number;
  first_name?: string;
  last_name?: string;
  email: string;
}

const TRANSACTION_TYPES = [
  { value: 'income', label: 'Receita' },
  { value: 'expense', label: 'Despesa' },
  { value: 'investment', label: 'Investimento' },
];

const TRANSACTION_SUBTYPES = [
  { value: 'purchase', label: 'Compra', types: ['expense'] },
  { value: 'refund', label: 'Estorno/Reembolso', types: ['income'] },
  { value: 'payment', label: 'Pagamento', types: ['expense', 'income'] },
  { value: 'transfer_out', label: 'Transferência Enviada', types: ['expense'] },
  { value: 'transfer_in', label: 'Transferência Recebida', types: ['income'] },
  { value: 'investment_deposit', label: 'Aplicação', types: ['investment'] },
  { value: 'investment_withdrawal', label: 'Resgate', types: ['investment'] },
  { value: 'credit_card_payment', label: 'Pagamento de Fatura', types: ['expense'] },
  { value: 'credit_card_purchase', label: 'Compra no Cartão', types: ['expense'] },
  { value: 'tithe', label: 'Dízimo', types: ['expense'] },
  { value: 'donation', label: 'Doação', types: ['expense'] },
  { value: 'other', label: 'Outro', types: ['income', 'expense', 'investment'] },
];

const DEFAULT_CATEGORIES = [
  'Alimentação',
  'Transporte',
  'Moradia',
  'Saúde',
  'Educação',
  'Lazer',
  'Investimentos',
  'Cartão de Crédito',
  'Outros',
];

const DEFAULT_FORM_DATA: TransactionFormData = {
  description: '',
  amount: undefined as unknown as number,
  type: 'expense',
  category: 'Outros',
  date: new Date(),
  subtype: 'purchase',
  credit_card_id: null,
  investment_id: null,
  related_transaction_id: null,
  owner_id: null
};

const TransactionForm: React.FC<TransactionFormProps> = ({ open, onClose, onSubmit, transaction }) => {
  const [formData, setFormData] = useState<TransactionFormData>({...DEFAULT_FORM_DATA});
  const [creditCards, setCreditCards] = useState<CreditCard[]>([]);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [selectedCreditCard, setSelectedCreditCard] = useState<CreditCard | null>(null);
  const [selectedOwner, setSelectedOwner] = useState<FamilyMember | null>(null);

  useEffect(() => {
    if (open) {
      fetchCreditCards();
      fetchInvestments();
      fetchFamilyMembers();
    }

    if (open && !transaction) {
      setFormData({
        description: '',
        amount: 0,
        type: 'expense',
        category: '',
        date: new Date(),
        subtype: 'purchase',
        credit_card_id: null,
        investment_id: null,
        related_transaction_id: null,
        owner_id: null,
      });
      setErrors({});
    }

    if (open && transaction) {
      try {
        setFormData({
          description: transaction.description,
          amount: transaction.amount,
          type: transaction.type,
          category: transaction.category,
          date: new Date(transaction.date),
          subtype: (transaction.subtype || 'other') as 'purchase' | 'refund' | 'payment' | 'transfer_in' | 'transfer_out' | 'investment_deposit' | 'investment_withdrawal' | 'credit_card_payment' | 'credit_card_purchase' | 'tithe' | 'donation' | 'other',
          credit_card_id: transaction.credit_card_id || null,
          investment_id: transaction.investment_id || null,
          related_transaction_id: transaction.related_transaction_id || null,
          owner_id: transaction.owner_id || null,
        });
        setErrors({});

        if (transaction.type === 'investment' && transaction.investment_id) {
          loadInvestmentDetails(transaction.investment_id);
        }

        if (transaction.credit_card_id) {
          loadCreditCardDetails(transaction.credit_card_id);
        }
      } catch (error) {
        console.error('Erro ao preencher formulário:', error);
      }
    }
  }, [open, transaction]);

  const loadInvestmentDetails = async (investmentId: number) => {
    try {
      const response = await axios.get(`/api/v1/investments/${investmentId}`);
      setSelectedInvestment(response.data);
    } catch (error) {
      console.error('Erro ao carregar detalhes do investimento:', error);
    }
  };

  const loadCreditCardDetails = async (cardId: number) => {
    try {
      const response = await axios.get(`/api/v1/credit-cards/${cardId}`);
      setSelectedCreditCard(response.data);
    } catch (error) {
      console.error('Erro ao carregar detalhes do cartão:', error);
    }
  };

  const fetchCreditCards = async () => {
    try {
      const response = await axios.get('/api/v1/credit-cards/');
      setCreditCards(response.data);
    } catch (error) {
      console.error('Erro ao carregar cartões de crédito:', error);
    }
  };

  const fetchInvestments = async () => {
    try {
      const response = await axios.get('/api/v1/investments/');
      setInvestments(response.data);
    } catch (error) {
      console.error('Erro ao carregar investimentos:', error);
    }
  };

  const fetchFamilyMembers = async () => {
    try {
      const response = await axios.get('/api/v1/family/members');
      setFamilyMembers(response.data);
    } catch (error) {
      console.error('Erro ao carregar membros da família:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('Enviando dados:', formData);
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Erro ao submeter formulário:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value === '' ? '' : parseFloat(value) || 0
    }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleDateChange = (newDate: Date | null) => {
    try {
      if (newDate && !isNaN(newDate.getTime())) {
        setFormData(prev => ({
          ...prev,
          date: newDate
        }));
      }
    } catch (error) {
      console.error('Erro ao alterar data:', error);
    }
  };

  const handleInvestmentChange = (_event: React.SyntheticEvent, value: Investment | null) => {
    setFormData(prev => ({
      ...prev,
      investment_id: value ? value.id : null,
      category: value ? 'Investimentos' : prev.category
    }));
    setSelectedInvestment(value);
  };

  const handleCreditCardChange = (_event: React.SyntheticEvent, value: CreditCard | null) => {
    setFormData(prev => ({
      ...prev,
      credit_card_id: value ? value.id : null
    }));
    setSelectedCreditCard(value);
  };

  const handleOwnerChange = (_event: React.SyntheticEvent, value: FamilyMember | null) => {
    setFormData(prev => ({
      ...prev,
      owner_id: value ? value.id : null
    }));
    setSelectedOwner(value);
  };

  const availableSubtypes = TRANSACTION_SUBTYPES.filter(
    subtype => subtype.types.includes(formData.type)
  );

  const renderTypeSpecificFields = () => {
    switch (formData.type) {
      case 'investment':
        return (
          <Grid item xs={12}>
            <Autocomplete
              options={investments}
              getOptionLabel={(option) => option.name}
              value={selectedInvestment}
              onChange={handleInvestmentChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Investimento (Opcional)"
                  fullWidth
                  error={!!errors.investment_id}
                  helperText={errors.investment_id || "Deixe em branco para criar um investimento genérico"}
                />
              )}
            />
          </Grid>
        );
      case 'expense':
        if (formData.subtype === 'credit_card_purchase' || formData.subtype === 'credit_card_payment') {
          return (
            <Grid item xs={12}>
              <Autocomplete
                options={creditCards}
                getOptionLabel={(option) => `${option.name} ${option.last_digits ? `(${option.last_digits})` : ''}`}
                value={selectedCreditCard}
                onChange={handleCreditCardChange}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Cartão de Crédito"
                    fullWidth
                    required={formData.subtype === 'credit_card_purchase'}
                    error={!!errors.credit_card_id}
                    helperText={errors.credit_card_id}
                  />
                )}
              />
            </Grid>
          );
        }
        return null;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{transaction ? 'Editar Transação' : 'Nova Transação'}</DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              name="description"
              label="Descrição"
              value={formData.description}
              onChange={handleChange}
              fullWidth
              required
              error={!!errors.description}
              helperText={errors.description}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!errors.type}>
              <InputLabel id="type-label">Tipo</InputLabel>
              <Select
                labelId="type-label"
                id="type"
                name="type"
                value={formData.type}
                onChange={handleSelectChange}
                label="Tipo"
              >
                {TRANSACTION_TYPES.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
              {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!errors.subtype}>
              <InputLabel id="subtype-label">Subtipo</InputLabel>
              <Select
                labelId="subtype-label"
                id="subtype"
                name="subtype"
                value={formData.subtype}
                onChange={handleSelectChange}
                label="Subtipo"
              >
                {availableSubtypes.map((subtype) => (
                  <MenuItem key={subtype.value} value={subtype.value}>
                    {subtype.label}
                  </MenuItem>
                ))}
              </Select>
              {errors.subtype && <FormHelperText>{errors.subtype}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              name="amount"
              label="Valor"
              value={formData.amount}
              onChange={handleNumberChange}
              fullWidth
              required
              type="number"
              InputProps={{
                startAdornment: <InputAdornment position="start">R$</InputAdornment>,
              }}
              error={!!errors.amount}
              helperText={errors.amount}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <DatePicker
                label="Data"
                value={formData.date}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: 'outlined'
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <Autocomplete
                options={DEFAULT_CATEGORIES}
                value={formData.category}
                onChange={(_, newValue) => {
                  setFormData(prev => ({
                    ...prev,
                    category: newValue || ''
                  }));
                  if (errors.category) {
                    setErrors(prev => ({ ...prev, category: '' }));
                  }
                }}
                freeSolo
                sx={{ flexGrow: 1 }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Categoria"
                    required
                    error={!!errors.category}
                    helperText={errors.category}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData(prev => ({
                        ...prev,
                        category: value
                      }));
                      if (errors.category) {
                        setErrors(prev => ({ ...prev, category: '' }));
                      }
                    }}
                  />
                )}
              />
              <AICategorization
                description={formData.description}
                amount={formData.amount}
                onCategorySuggested={(category) => {
                  setFormData(prev => ({
                    ...prev,
                    category
                  }));
                  if (errors.category) {
                    setErrors(prev => ({ ...prev, category: '' }));
                  }
                }}
                disabled={!formData.description || !formData.amount}
              />
            </Box>
          </Grid>

          {/* Campo para selecionar o dono da transação */}
          <Grid item xs={12}>
            <Autocomplete
              options={[{ id: 0, first_name: 'Você', last_name: '', email: '' }, ...familyMembers]}
              getOptionLabel={(option) =>
                option.id === 0 ? 'Você' :
                `${option.first_name || ''} ${option.last_name || ''}`.trim() || option.email.split('@')[0]
              }
              value={selectedOwner || { id: 0, first_name: 'Você', last_name: '', email: '' }}
              onChange={handleOwnerChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Dono da Transação"
                  fullWidth
                  helperText="Selecione quem é o dono real desta transação"
                />
              )}
            />
          </Grid>

          {renderTypeSpecificFields()}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {transaction ? 'Atualizar' : 'Criar'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TransactionForm;