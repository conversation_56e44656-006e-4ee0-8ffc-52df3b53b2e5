import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Snackbar,
  Alert,
  Paper
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useFilters } from '../contexts/FiltersContext';
import GlobalFilters from '../components/GlobalFilters/GlobalFilters';
import CollapsibleNavBar from '../components/Navigation/CollapsibleNavBar';
import FamilyManager from '../components/Family/FamilyManager';
import api from '../services/api';

const Family: React.FC = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [feedback, setFeedback] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const { filters } = useFilters();

  useEffect(() => {
    let mounted = true;

    const checkAdminStatus = async () => {
      try {
        const response = await api.get('/auth/users/me');
        if (mounted && response.data) {
          setIsAdmin(response.data.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de admin:', error);
        if (mounted) {
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, []);

  const handleCloseFeedback = () => {
    setFeedback(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Barra de Navegação Colapsável */}
      <CollapsibleNavBar isAdmin={isAdmin} title="Finance App - Família" />

      {/* Container centralizado com 80% de largura para todo o conteúdo */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Typography variant="h4" gutterBottom>
          Gerenciamento Familiar
        </Typography>

        {/* Filtros Globais */}
        <GlobalFilters
          filters={filters}
          onFiltersChange={() => {}} // Será gerenciado pelo contexto
          showFamilyFilter={true}
        />

        {/* Gerenciador de Família */}
        <Paper sx={{ p: 4 }}>
          <FamilyManager />
        </Paper>

        <Snackbar
          open={feedback.open}
          autoHideDuration={6000}
          onClose={handleCloseFeedback}
        >
          <Alert
            onClose={handleCloseFeedback}
            severity={feedback.severity}
            variant="filled"
          >
            {feedback.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Family;
