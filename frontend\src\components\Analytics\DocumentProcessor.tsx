import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  TextField,
  CircularProgress,
  Checkbox,
  Step,
  StepLabel,
  Stepper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import axios from 'axios';
import { CloudUpload as UploadIcon, ContentCopy, Add } from '@mui/icons-material';

// String literal types to match Dashboard.tsx
type TransactionType = 'income' | 'expense' | 'investment';
type TransactionSubType = 'purchase' | 'refund' | 'payment' | 'transfer_in' | 'transfer_out' | 'investment_deposit' | 'investment_withdrawal' | 'credit_card_payment' | 'credit_card_purchase' | 'tithe' | 'donation' | 'other';

// Constants for transaction types
const TRANSACTION_TYPE = {
  INCOME: 'income' as TransactionType,
  EXPENSE: 'expense' as TransactionType,
  INVESTMENT: 'investment' as TransactionType
};

// Constants for transaction subtypes
const TRANSACTION_SUBTYPE = {
  PURCHASE: 'purchase' as TransactionSubType,
  REFUND: 'refund' as TransactionSubType,
  PAYMENT: 'payment' as TransactionSubType,
  TRANSFER_IN: 'transfer_in' as TransactionSubType,
  TRANSFER_OUT: 'transfer_out' as TransactionSubType,
  INVESTMENT_DEPOSIT: 'investment_deposit' as TransactionSubType,
  INVESTMENT_WITHDRAWAL: 'investment_withdrawal' as TransactionSubType,
  CREDIT_CARD_PAYMENT: 'credit_card_payment' as TransactionSubType,
  CREDIT_CARD_PURCHASE: 'credit_card_purchase' as TransactionSubType,
  TITHE: 'tithe' as TransactionSubType,
  DONATION: 'donation' as TransactionSubType,
  OTHER: 'other' as TransactionSubType
};

// Mapeamento de tipos para subtipos disponíveis
const SUBTYPE_BY_TYPE = {
  [TRANSACTION_TYPE.INCOME]: [
    TRANSACTION_SUBTYPE.REFUND,
    TRANSACTION_SUBTYPE.PAYMENT,
    TRANSACTION_SUBTYPE.TRANSFER_IN,
    TRANSACTION_SUBTYPE.INVESTMENT_WITHDRAWAL,
    TRANSACTION_SUBTYPE.OTHER
  ],
  [TRANSACTION_TYPE.EXPENSE]: [
    TRANSACTION_SUBTYPE.PURCHASE,
    TRANSACTION_SUBTYPE.PAYMENT,
    TRANSACTION_SUBTYPE.TRANSFER_OUT,
    TRANSACTION_SUBTYPE.CREDIT_CARD_PAYMENT,
    TRANSACTION_SUBTYPE.CREDIT_CARD_PURCHASE,
    TRANSACTION_SUBTYPE.TITHE,
    TRANSACTION_SUBTYPE.DONATION,
    TRANSACTION_SUBTYPE.OTHER
  ],
  [TRANSACTION_TYPE.INVESTMENT]: [
    TRANSACTION_SUBTYPE.INVESTMENT_DEPOSIT,
    TRANSACTION_SUBTYPE.INVESTMENT_WITHDRAWAL,
    TRANSACTION_SUBTYPE.OTHER
  ]
};

// Rótulos em português para os subtipos
const SUBTYPE_LABELS = {
  [TRANSACTION_SUBTYPE.PURCHASE]: 'Compra',
  [TRANSACTION_SUBTYPE.REFUND]: 'Estorno/Reembolso',
  [TRANSACTION_SUBTYPE.PAYMENT]: 'Pagamento',
  [TRANSACTION_SUBTYPE.TRANSFER_IN]: 'Transferência Recebida',
  [TRANSACTION_SUBTYPE.TRANSFER_OUT]: 'Transferência Enviada',
  [TRANSACTION_SUBTYPE.INVESTMENT_DEPOSIT]: 'Aplicação',
  [TRANSACTION_SUBTYPE.INVESTMENT_WITHDRAWAL]: 'Resgate',
  [TRANSACTION_SUBTYPE.CREDIT_CARD_PAYMENT]: 'Pagamento de Fatura',
  [TRANSACTION_SUBTYPE.CREDIT_CARD_PURCHASE]: 'Compra no Cartão',
  [TRANSACTION_SUBTYPE.TITHE]: 'Dízimo',
  [TRANSACTION_SUBTYPE.DONATION]: 'Doação',
  [TRANSACTION_SUBTYPE.OTHER]: 'Outro'
};

interface Transaction {
  id: number; // Changed from optional to required
  description: string;
  amount: number;
  type: TransactionType;
  category: string;
  date: string;
  subtype: TransactionSubType;
  newCategoryName?: string;
  // Novos campos
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  is_investment?: boolean;
  is_duplicate?: boolean; // Flag para indicar se a transação é duplicada
  is_refund?: boolean; // Flag para identificar estornos
}

interface DocumentProcessorProps {
  onTransactionsExtracted: (transactions: Transaction[]) => void;
}

// Enum para controlar as etapas do processo
enum ProcessingStep {
  UPLOAD = 'upload',
  REVIEW = 'review',
  CONFIRM = 'confirm'
}

const DocumentProcessor: React.FC<DocumentProcessorProps> = ({ onTransactionsExtracted: _ }) => {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [loading, setLoading] = useState(false);
  const [extractedTransactions, setExtractedTransactions] = useState<Transaction[]>([]);
  const [statusMessage, setStatusMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [currentStep, setCurrentStep] = useState<ProcessingStep>(ProcessingStep.UPLOAD);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [selectedTransactionIds, setSelectedTransactionIds] = useState<number[]>([]);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  useEffect(() => {
    const total = extractedTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);
    setTotalAmount(total);
  }, [extractedTransactions]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFiles(event.target.files);
      setErrorMessage('');
      setProcessingProgress(0);

      // Processar automaticamente os documentos após a seleção
      setTimeout(() => {
        if (event.target.files) {
          processAndSaveDocuments(event.target.files);
        }
      }, 300);
    } else {
      setSelectedFiles(null);
    }
  };

  // Atualização da função para aceitar arquivos como parâmetro opcional
  const processAndSaveDocuments = async (files?: FileList) => {
    const filesToProcess = files || selectedFiles;

    if (!filesToProcess || filesToProcess.length === 0) {
      setErrorMessage('Por favor, selecione um ou mais documentos para processar.');
      return;
    }

    const authToken = localStorage.getItem('@FinanceApp:token');
    if (!authToken) {
      setErrorMessage('Sua sessão expirou. Por favor, faça login novamente.');
      return;
    }

    setLoading(true);
    setStatusMessage('Enviando documentos para processamento...');
    setErrorMessage('');
    setProcessingProgress(10);

    const formData = new FormData();
    for (let i = 0; i < filesToProcess.length; i++) {
      formData.append('files', filesToProcess[i]);
    }

    // Sempre usar extract_only como true para nosso fluxo
    // Isso garante que as transações não serão salvas automaticamente
    formData.append('extract_only', 'true');
    console.log("Parâmetro extract_only definido como 'true'");

    // Verificar se o parâmetro foi adicionado corretamente
    const hasExtractOnly = formData.has('extract_only');
    const extractOnlyValue = formData.get('extract_only');
    console.log(`Verificação do parâmetro extract_only: presente=${hasExtractOnly}, valor=${extractOnlyValue}`);

    console.log("Iniciando processamento de documentos:", {
      numFiles: filesToProcess.length,
      formDataKeys: Array.from(formData.keys())
    });

    try {
      setProcessingProgress(30);
      console.log("Fazendo requisição para /api/v1/documents/process/");
      const response = await axios.post('/api/v1/documents/process/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${authToken}`
        },
      });

      setProcessingProgress(70);
      console.log("Resposta recebida do servidor:", response.data);
      setStatusMessage(`Documentos processados com sucesso! Extraídas ${response.data.transactions.length} transações.`);

      // Formatar corretamente as transações e incluir a flag is_duplicate
      const transactions = response.data.transactions.map((trans: any): Transaction => {
        console.log("Processando transação:", trans);
        return {
          id: trans.id || Math.floor(Math.random() * -1000),
          description: trans.description || '',
          // Corrigir formatação dos valores para garantir duas casas decimais
          amount: typeof trans.amount === 'number' ? parseFloat(trans.amount.toFixed(2)) : parseFloat(parseFloat(trans.amount).toFixed(2)),
          type: trans.type || TRANSACTION_TYPE.EXPENSE,
          category: trans.category ? (trans.category.charAt(0).toUpperCase() + trans.category.slice(1)) : 'Outros',
          date: trans.date || new Date().toISOString(),
          subtype: trans.subtype || TRANSACTION_SUBTYPE.OTHER,
          is_investment: false,
          is_duplicate: trans.is_duplicate || false, // Usar a flag de duplicado do backend
          is_refund: trans.is_refund || false // Usar a flag de estorno do backend
        };
      });

      console.log("Transações após mapeamento inicial:", transactions);

      // Preencher dados adicionais das transações
      const processedTransactions = transactions.map((trans: Transaction) => {
        const desc = trans.description.toLowerCase();
        const isInvestment =
          desc.includes('aplicação') ||
          desc.includes('resgate') ||
          desc.includes('investimento') ||
          desc.includes('rdb') ||
          desc.includes('cdb') ||
          desc.includes('tesouro');

        return {
          ...trans,
          is_investment: isInvestment
        };
      });

      console.log("Transações processadas finais:", processedTransactions);

      // Garantir que a transição para a tela de revisão ocorra logo após o processamento
      setExtractedTransactions(processedTransactions);
      setCurrentStep(ProcessingStep.REVIEW);
      setProcessingProgress(100);

      // Removido a chamada automática de onTransactionsExtracted para permitir revisão antes de salvar

    } catch (error: any) {
      console.error('Erro ao processar documentos:', error);
      setProcessingProgress(0);

      let errorMsg = 'Erro ao processar documentos. Verifique se os arquivos são válidos.';

      if (error.response) {
        console.error("Detalhes do erro:", error.response);
        if (error.response.data && error.response.data.detail) {
          if (typeof error.response.data.detail === 'object') {
            errorMsg = error.response.data.detail.message || errorMsg;
          } else {
            errorMsg = error.response.data.detail;
          }
        }

        if (error.response.status === 413) {
          errorMsg = 'O arquivo é muito grande. Por favor, envie um arquivo menor.';
        } else if (error.response.status === 415) {
          errorMsg = 'Formato de arquivo não suportado. Use PDF, JPG, PNG ou CSV.';
        } else if (error.response.status === 401) {
          errorMsg = 'Sua sessão expirou. Por favor, faça login novamente.';
        }
      } else if (error.request) {
        errorMsg = 'Não foi possível conectar ao servidor. Verifique sua conexão com a internet.';
      }

      setErrorMessage(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedFiles(null);
    setExtractedTransactions([]);
    setErrorMessage('');
    setStatusMessage('');
    setCurrentStep(ProcessingStep.UPLOAD);
    setProcessingProgress(0);
    if (document.getElementById('file-input') as HTMLInputElement) {
      (document.getElementById('file-input') as HTMLInputElement).value = '';
    }
  };

  const updateTransaction = (index: number, data: Partial<Transaction>) => {
    setExtractedTransactions(prev =>
      prev.map((trans, i) => i === index ? { ...trans, ...data } : trans)
    );
  };

  const handleTypeChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const type = event.target.value as TransactionType;

    // Quando o tipo muda, precisamos atualizar o subtipo para um valor válido para o novo tipo
    const availableSubtypes = SUBTYPE_BY_TYPE[type];
    const currentSubtype = extractedTransactions[index].subtype;

    // Verificar se o subtipo atual é válido para o novo tipo
    const isCurrentSubtypeValid = availableSubtypes.includes(currentSubtype);

    // Se o subtipo atual não for válido para o novo tipo, usar o primeiro subtipo disponível
    const newSubtype = isCurrentSubtypeValid ? currentSubtype : availableSubtypes[0];

    // Atualizar o tipo e o subtipo
    updateTransaction(index, { type, subtype: newSubtype });
  };

  const handleSubtypeChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const subtype = event.target.value as TransactionSubType;
    updateTransaction(index, { subtype });
  };

  const handleCategoryChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    let category = event.target.value;

    // Capitalizar a primeira letra se a categoria não estiver vazia
    if (category.length > 0) {
      category = category.charAt(0).toUpperCase() + category.slice(1);
    }

    updateTransaction(index, { category });
  };

  const handleSelectTransaction = (id: number) => {
    setSelectedTransactionIds(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedTransactionIds.length === extractedTransactions.length) {
      setSelectedTransactionIds([]);
    } else {
      setSelectedTransactionIds(extractedTransactions.map(t => t.id));
    }
  };

  const handleDeleteSelected = () => {
    setExtractedTransactions(prev =>
      prev.filter(t => !selectedTransactionIds.includes(t.id))
    );
    setSelectedTransactionIds([]);
  };

  const handleAddNewTransaction = () => {
    const newId = Math.floor(Math.random() * -1000) - 1;
    const defaultType = TRANSACTION_TYPE.EXPENSE;
    const defaultSubtype = SUBTYPE_BY_TYPE[defaultType][0]; // Pegar o primeiro subtipo válido para o tipo padrão

    const newTransaction: Transaction = {
      id: newId,
      description: '',
      amount: 0,
      type: defaultType,
      category: 'Outros',
      date: new Date().toISOString(),
      subtype: defaultSubtype,
      is_investment: false
    };

    setExtractedTransactions(prev => [...prev, newTransaction]);
  };

  const handleReplicateSubtype = (subtype: TransactionSubType) => {
    setExtractedTransactions(prev =>
      prev.map(t => {
        // Verificar se o subtipo é válido para o tipo da transação
        const isValidSubtype = SUBTYPE_BY_TYPE[t.type].includes(subtype);

        // Se for válido, usar o subtipo fornecido; caso contrário, manter o subtipo atual
        return isValidSubtype ? { ...t, subtype } : t;
      })
    );
  };

  const handleReplicateCategory = (category: string) => {
    // Capitalizar a primeira letra se a categoria não estiver vazia
    let formattedCategory = category;
    if (formattedCategory.length > 0) {
      formattedCategory = formattedCategory.charAt(0).toUpperCase() + formattedCategory.slice(1);
    }

    setExtractedTransactions(prev =>
      prev.map(t => ({ ...t, category: formattedCategory }))
    );
  };

  const handleDescriptionChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    updateTransaction(index, { description: event.target.value });
  };

  const handleAmountChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    if (!isNaN(value)) {
      updateTransaction(index, { amount: value });
    }
  };

  const handleDateChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const date = new Date(event.target.value + 'T00:00:00');
      if (!isNaN(date.getTime())) {
        updateTransaction(index, { date: date.toISOString() });
      }
    } catch (e) {
      console.error('Data inválida:', e);
    }
  };

  // Função para renderizar a lista de arquivos selecionados
  const renderFileList = () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      return <Typography color="text.secondary">Nenhum arquivo selecionado</Typography>;
    }

    return (
      <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
          Arquivos selecionados ({selectedFiles.length}):
        </Typography>
        <Box sx={{ maxHeight: '150px', overflow: 'auto' }}>
          {Array.from(selectedFiles).map((file, index) => (
            <Box key={index} sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ flexGrow: 1 }}>
                {file.name}
                <Typography variant="caption" color="text.secondary" component="span" sx={{ ml: 1 }}>
                  ({(file.size / 1024).toFixed(2)} KB - {file.type || 'Tipo desconhecido'})
                </Typography>
              </Typography>
            </Box>
          ))}
        </Box>
      </Paper>
    );
  };

  const renderSteps = () => (
    <Stepper activeStep={
      currentStep === ProcessingStep.UPLOAD ? 0 :
      currentStep === ProcessingStep.REVIEW ? 1 : 2
    } sx={{ mb: 3 }}>
      <Step>
        <StepLabel>Selecionar Documentos</StepLabel>
      </Step>
      <Step>
        <StepLabel>Revisar Transações</StepLabel>
      </Step>
      <Step>
        <StepLabel>Confirmação</StepLabel>
      </Step>
    </Stepper>
  );

  const renderCurrentStep = () => {
    console.log("Renderizando etapa atual:", currentStep);
    console.log("Número de transações:", extractedTransactions.length);

    switch (currentStep) {
      case ProcessingStep.UPLOAD:
        return (
          <>
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
              <input
                accept=".pdf,.jpg,.jpeg,.png,.csv"
                style={{ display: 'none' }}
                id="file-input"
                multiple
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="file-input">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<UploadIcon />}
                  sx={{ width: '300px', whiteSpace: 'nowrap' }}
                >
                  Selecionar Documentos
                </Button>
              </label>
            </Box>

            {renderFileList()}
          </>
        );

      case ProcessingStep.REVIEW:
        console.log("Renderizando etapa de REVIEW");
        console.log("Transações disponíveis:", extractedTransactions);
        console.log("Estado de loading:", loading);
        return (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Transações Extraídas ({extractedTransactions.length})
                </Typography>
                <Typography variant="body2">
                  Total de transações: {extractedTransactions.length}
                </Typography>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 1 }}>
                  Valor Total: R$ {totalAmount.toFixed(2)}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 4 }}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    // Salvar transações e ir para a confirmação
                    const authToken = localStorage.getItem('@FinanceApp:token');
                    if (!authToken) {
                      setErrorMessage('Sua sessão expirou. Por favor, faça login novamente.');
                      return;
                    }

                    setLoading(true);
                    setStatusMessage('Salvando transações...');
                    setErrorMessage('');
                    setProcessingProgress(50);

                    // Filtrar transações não-duplicadas para salvar
                    const transactionsToSave = extractedTransactions.filter(t => !t.is_duplicate);

                    axios.post('/api/v1/transactions/bulk/', {
                      transactions: transactionsToSave
                    }, {
                      headers: {
                        'Authorization': `Bearer ${authToken}`
                      }
                    })
                    .then(() => {
                      setProcessingProgress(100);
                      setStatusMessage('Transações salvas com sucesso!');
                      setCurrentStep(ProcessingStep.CONFIRM);

                      // NÃO chamamos onTransactionsExtracted para evitar que o Dashboard tente salvar as transações novamente
                      // Apenas disparamos um evento para atualizar a lista de transações
                      const event = new CustomEvent('refreshTransactions');
                      window.dispatchEvent(event);
                    })
                    .catch((error) => {
                      console.error('Erro ao salvar transações:', error);
                      setErrorMessage('Erro ao salvar transações. Por favor verifique os dados e tente novamente.');
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                  }}
                >
                  Salvar Transações
                </Button>

                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  onClick={handleDeleteSelected}
                  disabled={selectedTransactionIds.length === 0}
                  sx={{ color: 'white' }}
                >
                  Excluir Selecionadas {selectedTransactionIds.length > 0 ? `(${selectedTransactionIds.length})` : ''}
                </Button>
              </Box>
            </Box>

            <Paper variant="outlined" sx={{ mb: 3, overflow: 'hidden' }}>
              <Box sx={{ overflowX: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow sx={{ '& th': { py: 2 } }}>
                      <TableCell padding="checkbox" sx={{ pl: 2 }}>
                        <Checkbox
                          checked={selectedTransactionIds.length === extractedTransactions.length && extractedTransactions.length > 0}
                          indeterminate={selectedTransactionIds.length > 0 && selectedTransactionIds.length < extractedTransactions.length}
                          onChange={handleSelectAll}
                        />
                      </TableCell>
                      <TableCell sx={{ pl: 2 }}>Descrição</TableCell>
                      <TableCell sx={{ pl: 2 }}>Valor</TableCell>
                      <TableCell sx={{ pl: 2 }}>Data</TableCell>
                      <TableCell sx={{ pl: 2 }}>
                        Tipo
                      </TableCell>
                      <TableCell sx={{ pl: 2 }}>
                        Subtipo
                        <IconButton
                          size="small"
                          title="Replicar para todos"
                          sx={{ ml: 1, color: 'primary.main' }}
                          disabled={extractedTransactions.length === 0}
                          onClick={() => extractedTransactions[0] && handleReplicateSubtype(extractedTransactions[0].subtype)}
                        >
                          <ContentCopy fontSize="small" />
                        </IconButton>
                      </TableCell>
                      <TableCell sx={{ pl: 2 }}>
                        Categoria
                        <IconButton
                          size="small"
                          title="Replicar para todos"
                          sx={{ ml: 1, color: 'secondary.main' }}
                          disabled={extractedTransactions.length === 0}
                          onClick={() => extractedTransactions[0] && handleReplicateCategory(extractedTransactions[0].category)}
                        >
                          <ContentCopy fontSize="small" />
                        </IconButton>
                      </TableCell>
                      <TableCell sx={{ pl: 2 }}>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {extractedTransactions && extractedTransactions.length > 0 ? (
                      extractedTransactions.map((transaction, index) => (
                        <React.Fragment key={transaction.id || index}>
                          <TableRow
                            hover
                            selected={selectedTransactionIds.includes(transaction.id)}
                            sx={{
                              // Adicionar um estilo específico para linhas duplicadas
                              backgroundColor: transaction.is_duplicate ? 'rgba(255, 244, 229, 0.7)' : 'inherit'
                            }}
                          >
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={selectedTransactionIds.includes(transaction.id)}
                                onChange={() => handleSelectTransaction(transaction.id)}
                              />
                            </TableCell>
                            <TableCell>
                              {/* Adicionar indicador visual para transações duplicadas */}
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <TextField
                                  value={transaction.description}
                                  onChange={(e) => handleDescriptionChange(index, e as any)}
                                  size="small"
                                  sx={{ width: '220px' }}
                                  variant="outlined"
                                  error={transaction.is_duplicate}
                                />
                              </Box>
                            </TableCell>
                            <TableCell>
                              <TextField
                                value={transaction.amount.toFixed(2)} // Garantir duas casas decimais
                                onChange={(e) => handleAmountChange(index, e as any)}
                                size="small"
                                type="number"
                                inputProps={{ step: "0.01", min: "0" }}
                                sx={{ width: '180px' }}
                                variant="outlined"
                                error={transaction.is_duplicate}
                              />
                            </TableCell>
                            <TableCell>
                              <TextField
                                type="date"
                                value={new Date(transaction.date).toISOString().split('T')[0]}
                                onChange={(e) => handleDateChange(index, e as any)}
                                size="small"
                                sx={{ width: '180px' }}
                                variant="outlined"
                                error={transaction.is_duplicate}
                              />
                            </TableCell>
                            <TableCell>
                              <TextField
                                select
                                value={transaction.type}
                                onChange={(e) => handleTypeChange(index, e as any)}
                                size="small"
                                sx={{ width: '180px' }}
                                variant="outlined"
                                SelectProps={{
                                  native: true,
                                }}
                              >
                                <option value={TRANSACTION_TYPE.INCOME}>Receita</option>
                                <option value={TRANSACTION_TYPE.EXPENSE}>Despesa</option>
                                <option value={TRANSACTION_TYPE.INVESTMENT}>Investimento</option>
                              </TextField>
                            </TableCell>
                            <TableCell>
                              <TextField
                                select
                                value={transaction.subtype}
                                onChange={(e) => handleSubtypeChange(index, e as any)}
                                size="small"
                                sx={{ width: '180px' }}
                                variant="outlined"
                                SelectProps={{
                                  native: true,
                                }}
                              >
                                {SUBTYPE_BY_TYPE[transaction.type].map(subtype => (
                                  <option key={subtype} value={subtype}>
                                    {SUBTYPE_LABELS[subtype]}
                                  </option>
                                ))}
                              </TextField>
                            </TableCell>
                            <TableCell>
                              <TextField
                                value={transaction.category}
                                onChange={(e) => handleCategoryChange(index, e as any)}
                                size="small"
                                sx={{ width: '180px' }}
                                variant="outlined"
                                error={transaction.is_duplicate}
                              />
                            </TableCell>
                            <TableCell>
                              {/* Indicador de status para transações duplicadas */}
                              {transaction.is_duplicate ? (
                                <Box
                                  sx={{
                                    backgroundColor: '#FFA726',
                                    color: 'white',
                                    fontWeight: 'bold',
                                    borderRadius: '4px',
                                    padding: '4px 8px',
                                    fontSize: '0.75rem',
                                    display: 'inline-block',
                                    textAlign: 'center'
                                  }}
                                >
                                  Duplicada
                                </Box>
                              ) : (
                                <Box
                                  sx={{
                                    backgroundColor: '#4CAF50',
                                    color: 'white',
                                    fontWeight: 'bold',
                                    borderRadius: '4px',
                                    padding: '4px 8px',
                                    fontSize: '0.75rem',
                                    display: 'inline-block',
                                    textAlign: 'center'
                                  }}
                                >
                                  Nova
                                </Box>
                              )}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell padding="checkbox" sx={{ borderBottom: 'none', py: 0, height: '30px' }}>
                              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                <Tooltip title="Adicionar nova linha" arrow placement="right">
                                  <IconButton
                                    size="small"
                                    sx={{
                                      borderRadius: '50%',
                                      height: '28px',
                                      width: '28px',
                                      color: 'text.disabled',
                                      bgcolor: 'grey.100',
                                      '&:hover': {
                                        backgroundColor: 'grey.200',
                                        color: 'text.primary'
                                      }
                                    }}
                                    onClick={() => {
                                      const newId = Math.floor(Math.random() * -1000) - 1;
                                      const newTransaction = {
                                        ...transaction,
                                        id: newId,
                                        description: '',
                                        amount: 0,
                                        is_duplicate: false // Novas transações adicionadas manualmente não são duplicadas
                                      };

                                      const newTransactions = [...extractedTransactions];
                                      newTransactions.splice(index + 1, 0, newTransaction);
                                      setExtractedTransactions(newTransactions);
                                    }}
                                  >
                                    <Add fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </TableCell>
                            <TableCell colSpan={7} sx={{ borderBottom: 'none', py: 0, height: '30px' }}></TableCell>
                          </TableRow>
                        </React.Fragment>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <Typography variant="body2" color="text.secondary">
                            Nenhuma transação encontrada
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Button
                variant="outlined"
                startIcon={<Add />}
                onClick={handleAddNewTransaction}
              >
                Adicionar Transação
              </Button>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    // Salvar transações e ir para a confirmação
                    const authToken = localStorage.getItem('@FinanceApp:token');
                    if (!authToken) {
                      setErrorMessage('Sua sessão expirou. Por favor, faça login novamente.');
                      return;
                    }

                    setLoading(true);
                    setStatusMessage('Salvando transações...');
                    setErrorMessage('');
                    setProcessingProgress(50);

                    // Filtrar transações não-duplicadas para salvar
                    const transactionsToSave = extractedTransactions.filter(t => !t.is_duplicate);

                    axios.post('/api/v1/transactions/bulk/', {
                      transactions: transactionsToSave
                    }, {
                      headers: {
                        'Authorization': `Bearer ${authToken}`
                      }
                    })
                    .then(() => {
                      setProcessingProgress(100);
                      setStatusMessage('Transações salvas com sucesso!');
                      setCurrentStep(ProcessingStep.CONFIRM);

                      // NÃO chamamos onTransactionsExtracted para evitar que o Dashboard tente salvar as transações novamente
                      // Apenas disparamos um evento para atualizar a lista de transações
                      const event = new CustomEvent('refreshTransactions');
                      window.dispatchEvent(event);
                    })
                    .catch((error) => {
                      console.error('Erro ao salvar transações:', error);
                      setErrorMessage('Erro ao salvar transações. Por favor verifique os dados e tente novamente.');
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                  }}
                >
                  Salvar Transações
                </Button>

                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  onClick={handleDeleteSelected}
                  disabled={selectedTransactionIds.length === 0}
                  sx={{ color: 'white' }}
                >
                  Excluir Selecionadas {selectedTransactionIds.length > 0 ? `(${selectedTransactionIds.length})` : ''}
                </Button>
              </Box>
            </Box>
          </>
        );

      case ProcessingStep.CONFIRM:
        return (
          <>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                py: 6,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              <Typography variant="h6" color="primary" gutterBottom>
                Processamento Concluído!
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {extractedTransactions.filter(t => !t.is_duplicate).length} transações foram processadas e salvas com sucesso.
                {extractedTransactions.some(t => t.is_duplicate) &&
                  ` (${extractedTransactions.filter(t => t.is_duplicate).length} transações duplicadas foram identificadas e não foram salvas novamente)`}
              </Typography>
              <Button
                variant="contained"
                onClick={resetForm}
                sx={{ mt: 2 }}
              >
                Processar Novos Documentos
              </Button>
            </Box>
          </>
        );

      default:
        console.log("Estado desconhecido:", currentStep);
        return (
          <Box>
            <Typography color="error">
              Erro: estado desconhecido
            </Typography>
            <Button
              variant="contained"
              onClick={resetForm}
            >
              Reiniciar
            </Button>
          </Box>
        );
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Processador de Documentos
      </Typography>

      {renderSteps()}

      {(loading || processingProgress > 0) && (
        <Box sx={{ mb: 2, textAlign: 'center' }}>
          <CircularProgress variant={processingProgress > 0 ? "determinate" : "indeterminate"} value={processingProgress} />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {statusMessage || 'Processando...'}
          </Typography>
        </Box>
      )}

      {errorMessage && (
        <Box sx={{ mb: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
          <Typography color="error">{errorMessage}</Typography>
        </Box>
      )}

      {renderCurrentStep()}
    </Paper>
  );
};

export default DocumentProcessor;