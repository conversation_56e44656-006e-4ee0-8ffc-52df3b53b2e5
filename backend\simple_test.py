#!/usr/bin/env python3

import asyncio
import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_assistance import finance_ai
from app.database import get_db
from app.models.financial import User

async def test_ai_limits():
    print("🧪 TESTING AI LIMITS DIRECTLY")
    print("=" * 40)

    try:
        # Test 1: Check if AI is available
        print("1. Checking AI availability...")
        is_available = await finance_ai.is_available()
        print(f"   AI Available: {is_available}")

        if not is_available:
            print("❌ AI not available - cannot test limits")
            return

        # Test 2: Get database session
        print("\n2. Getting database session...")
        db = next(get_db())

        # Test 3: Get admin user
        print("3. Getting admin user...")
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            print("❌ Admin user not found")
            return

        print(f"   Admin user ID: {admin_user.id}")

        # Test 4: Test spending limits
        print("\n4. Testing spending limits...")
        try:
            limits = await finance_ai.suggest_spending_limits(
                user_id=admin_user.id,
                db=db
            )
            print("✅ Spending limits generated successfully!")
            print(f"   Message: {limits.get('message', 'No message')}")
            print(f"   Number of limits: {len(limits.get('limits', []))}")

            # Show sample limits
            limits_list = limits.get('limits', [])
            if limits_list and len(limits_list) > 0:
                sample_limits = limits_list[:3]
                print("   Sample limits:")
                for limit in sample_limits:
                    print(f"     - {limit.get('category', 'Unknown')}: R$ {limit.get('limit', 0):.2f}")

        except Exception as e:
            print(f"❌ Error generating limits: {str(e)}")
            import traceback
            traceback.print_exc()

        print("\n" + "=" * 40)
        print("🎯 AI LIMITS TEST COMPLETED!")

    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_limits())
