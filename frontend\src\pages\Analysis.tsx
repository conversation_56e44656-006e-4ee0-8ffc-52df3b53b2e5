import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Snackbar,
  Alert
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useFilters } from '../contexts/FiltersContext';
import GlobalFilters from '../components/GlobalFilters/GlobalFilters';
import CollapsibleNavBar from '../components/Navigation/CollapsibleNavBar';
import FinancialAnalytics from '../components/Analytics/FinancialAnalytics';
import api from '../services/api';

interface Transaction {
  id: number;
  description: string;
  amount: number;
  type: 'income' | 'expense' | 'investment';
  category: string;
  date: string;
  subtype: string;
  credit_card_id?: number | null;
  investment_id?: number | null;
  related_transaction_id?: number | null;
  is_investment?: boolean;
  is_refund?: boolean;
  owner_id?: number | null;
  owner?: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
  };
}

const Analysis: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [feedback, setFeedback] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const { filters } = useFilters();
  const { getFilterParams } = useFilters();

  useEffect(() => {
    let mounted = true;

    const fetchTransactions = async () => {
      try {
        // Usar os filtros globais
        const params = getFilterParams();
        const url = `/transactions/${params.toString() ? '?' + params.toString() : ''}`;
        const response = await api.get<Transaction[]>(url);
        if (mounted) {
          setTransactions(response.data);
        }
      } catch (error) {
        if (mounted) {
          console.error('Erro ao carregar transações:', error);
          setFeedback({
            open: true,
            message: 'Erro ao carregar transações. Tente novamente.',
            severity: 'error'
          });
        }
      }
    };

    const checkAdminStatus = async () => {
      try {
        const response = await api.get('/auth/users/me');
        if (mounted && response.data) {
          setIsAdmin(response.data.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de admin:', error);
        if (mounted) {
          setIsAdmin(false);
        }
      }
    };

    fetchTransactions();
    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, [filters, getFilterParams]);

  const handleCloseFeedback = () => {
    setFeedback(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Barra de Navegação Colapsável */}
      <CollapsibleNavBar isAdmin={isAdmin} title="Finance App - Análise" />

      {/* Container centralizado com 80% de largura para todo o conteúdo */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Typography variant="h4" gutterBottom>
          Análise Financeira
        </Typography>

        {/* Filtros Globais */}
        <GlobalFilters
          filters={filters}
          onFiltersChange={() => {}} // Será gerenciado pelo contexto
          showFamilyFilter={true}
        />

        {/* Componente de Análise */}
        <FinancialAnalytics transactions={transactions} />

        <Snackbar
          open={feedback.open}
          autoHideDuration={6000}
          onClose={handleCloseFeedback}
        >
          <Alert
            onClose={handleCloseFeedback}
            severity={feedback.severity}
            variant="filled"
          >
            {feedback.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Analysis;
