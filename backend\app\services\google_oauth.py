import logging
from typing import Optional, Dict, Any
from google.auth.transport import requests
from google.oauth2 import id_token
from google_auth_oauthlib.flow import Flow
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ..models.financial import User as UserModel
from ..security import security_settings, create_access_token, get_password_hash
from ..database import get_db

logger = logging.getLogger(__name__)

class GoogleOAuthService:
    """Serviço para autenticação via Google OAuth2"""
    
    def __init__(self):
        self.client_id = security_settings.GOOGLE_CLIENT_ID
        self.client_secret = security_settings.GOOGLE_CLIENT_SECRET
        self.redirect_uri = security_settings.GOOGLE_REDIRECT_URI
        
        # Verificar se as configurações estão definidas
        if not self.client_id or not self.client_secret:
            logger.warning("Configurações do Google OAuth2 não estão definidas")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Google OAuth2 configurado e habilitado")
    
    def is_enabled(self) -> bool:
        """Verifica se o Google OAuth2 está habilitado"""
        return self.enabled
    
    def get_authorization_url(self) -> str:
        """
        Gera a URL de autorização do Google
        
        Returns:
            URL para redirecionamento do usuário
        """
        if not self.enabled:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Google OAuth2 não está configurado"
            )
        
        try:
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                },
                scopes=['openid', 'email', 'profile']
            )
            flow.redirect_uri = self.redirect_uri
            
            authorization_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )
            
            logger.info("URL de autorização do Google gerada com sucesso")
            return authorization_url
            
        except Exception as e:
            logger.error(f"Erro ao gerar URL de autorização: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Erro ao gerar URL de autorização"
            )
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verifica o token ID do Google e extrai informações do usuário
        
        Args:
            token: Token ID recebido do Google
            
        Returns:
            Informações do usuário extraídas do token
        """
        if not self.enabled:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Google OAuth2 não está configurado"
            )
        
        try:
            # Verificar o token ID
            idinfo = id_token.verify_oauth2_token(
                token, 
                requests.Request(), 
                self.client_id
            )
            
            # Verificar se o token é válido
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Token inválido')
            
            logger.info(f"Token do Google verificado para usuário: {idinfo.get('email')}")
            return idinfo
            
        except ValueError as e:
            logger.error(f"Token do Google inválido: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token do Google inválido"
            )
        except Exception as e:
            logger.error(f"Erro ao verificar token do Google: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Erro ao verificar token do Google"
            )
    
    async def authenticate_or_create_user(
        self, 
        google_user_info: Dict[str, Any], 
        db: Session
    ) -> Dict[str, Any]:
        """
        Autentica um usuário existente ou cria um novo baseado nas informações do Google
        
        Args:
            google_user_info: Informações do usuário do Google
            db: Sessão do banco de dados
            
        Returns:
            Dicionário com token de acesso e informações do usuário
        """
        email = google_user_info.get('email')
        name = google_user_info.get('name', '')
        given_name = google_user_info.get('given_name', '')
        family_name = google_user_info.get('family_name', '')
        picture = google_user_info.get('picture', '')
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email não fornecido pelo Google"
            )
        
        # Verificar se o usuário já existe
        existing_user = db.query(UserModel).filter(UserModel.email == email).first()
        
        if existing_user:
            # Usuário existe, fazer login
            if not existing_user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Conta desativada"
                )
            
            # Atualizar informações do Google se necessário
            if existing_user.google_id != google_user_info.get('sub'):
                existing_user.google_id = google_user_info.get('sub')
                existing_user.profile_picture = picture
                db.commit()
            
            logger.info(f"Login via Google realizado para usuário existente: {email}")
            user = existing_user
            
        else:
            # Criar novo usuário
            try:
                new_user = UserModel(
                    email=email,
                    username=email,  # Usar email como username
                    full_name=name,
                    first_name=given_name,
                    last_name=family_name,
                    google_id=google_user_info.get('sub'),
                    profile_picture=picture,
                    is_active=True,
                    is_verified=True,  # Usuários do Google são considerados verificados
                    hashed_password=get_password_hash("google_oauth_user")  # Senha placeholder
                )
                
                db.add(new_user)
                db.commit()
                db.refresh(new_user)
                
                logger.info(f"Novo usuário criado via Google OAuth: {email}")
                user = new_user
                
            except Exception as e:
                db.rollback()
                logger.error(f"Erro ao criar usuário via Google OAuth: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Erro ao criar conta"
                )
        
        # Gerar token de acesso
        access_token = create_access_token(data={"sub": user.email})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "username": user.username,
                "full_name": user.full_name,
                "profile_picture": user.profile_picture,
                "is_verified": user.is_verified
            }
        }

# Instância global do serviço
google_oauth_service = GoogleOAuthService()
