import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Snackbar,
  Divider
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  Family as FamilyIcon
} from '@mui/icons-material';
import api from '../../services/api';

interface FamilyLink {
  id: number;
  requester_id: number;
  invited_id: number;
  status: string;
  created_at: string;
  requester_name: string;
  requester_email: string;
  invited_name: string;
  invited_email: string;
}

interface FamilyMember {
  id: number;
  name: string;
  email: string;
  is_self: boolean;
}

const FamilyManager: React.FC = () => {
  const [invites, setInvites] = useState<FamilyLink[]>([]);
  const [members, setMembers] = useState<FamilyMember[]>([]);
  const [openInviteDialog, setOpenInviteDialog] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info'
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [invitesResponse, membersResponse] = await Promise.all([
        api.get('/family/invites'),
        api.get('/family/members')
      ]);
      setInvites(invitesResponse.data);
      setMembers(membersResponse.data);
    } catch (error) {
      console.error('Erro ao carregar dados familiares:', error);
      showSnackbar('Erro ao carregar dados', 'error');
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSendInvite = async () => {
    if (!inviteEmail.trim()) {
      showSnackbar('Digite um email válido', 'error');
      return;
    }

    setLoading(true);
    try {
      await api.post('/family/invite', { invited_email: inviteEmail });
      showSnackbar('Convite enviado com sucesso!', 'success');
      setInviteEmail('');
      setOpenInviteDialog(false);
      loadData();
    } catch (error: any) {
      const message = error.response?.data?.detail || 'Erro ao enviar convite';
      showSnackbar(message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRespondToInvite = async (inviteId: number, status: 'accepted' | 'rejected') => {
    try {
      await api.put(`/family/invites/${inviteId}`, { status });
      const action = status === 'accepted' ? 'aceito' : 'rejeitado';
      showSnackbar(`Convite ${action} com sucesso!`, 'success');
      loadData();
    } catch (error: any) {
      const message = error.response?.data?.detail || 'Erro ao responder convite';
      showSnackbar(message, 'error');
    }
  };

  const handleRemoveLink = async (linkId: number) => {
    if (!window.confirm('Tem certeza que deseja desvincular esta conta familiar?')) {
      return;
    }

    try {
      await api.delete(`/family/links/${linkId}`);
      showSnackbar('Vinculação removida com sucesso!', 'success');
      loadData();
    } catch (error: any) {
      const message = error.response?.data?.detail || 'Erro ao remover vinculação';
      showSnackbar(message, 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'success';
      case 'rejected': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'accepted': return 'Aceito';
      case 'rejected': return 'Rejeitado';
      case 'pending': return 'Pendente';
      default: return status;
    }
  };

  const pendingInvites = invites.filter(invite => invite.status === 'pending');
  const acceptedInvites = invites.filter(invite => invite.status === 'accepted');

  return (
    <Box>
      {/* Membros da Família */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <FamilyIcon sx={{ mr: 1 }} />
            Membros da Família
          </Typography>
          <Button
            variant="contained"
            startIcon={<PersonAddIcon />}
            onClick={() => setOpenInviteDialog(true)}
          >
            Convidar Membro
          </Button>
        </Box>

        {members.length > 0 ? (
          <List>
            {members.map((member, index) => (
              <ListItem key={member.id} divider={index < members.length - 1}>
                <ListItemText
                  primary={member.name || member.email}
                  secondary={member.email}
                />
                {member.is_self && (
                  <Chip label="Você" color="primary" size="small" />
                )}
              </ListItem>
            ))}
          </List>
        ) : (
          <Alert severity="info">
            Nenhum membro familiar vinculado. Convide alguém para começar!
          </Alert>
        )}
      </Paper>

      {/* Convites Pendentes */}
      {pendingInvites.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Convites Pendentes
          </Typography>
          <List>
            {pendingInvites.map((invite) => (
              <ListItem key={invite.id}>
                <ListItemText
                  primary={
                    invite.requester_email === members.find(m => m.is_self)?.email
                      ? `Convite enviado para ${invite.invited_email}`
                      : `Convite recebido de ${invite.requester_email}`
                  }
                  secondary={`Enviado em ${new Date(invite.created_at).toLocaleDateString('pt-BR')}`}
                />
                <ListItemSecondaryAction>
                  {invite.requester_email !== members.find(m => m.is_self)?.email && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton
                        color="success"
                        onClick={() => handleRespondToInvite(invite.id, 'accepted')}
                        title="Aceitar"
                      >
                        <CheckIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleRespondToInvite(invite.id, 'rejected')}
                        title="Rejeitar"
                      >
                        <CloseIcon />
                      </IconButton>
                    </Box>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* Histórico de Convites */}
      {acceptedInvites.length > 0 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Vinculações Ativas
          </Typography>
          <List>
            {acceptedInvites.map((invite) => (
              <ListItem key={invite.id}>
                <ListItemText
                  primary={
                    invite.requester_email === members.find(m => m.is_self)?.email
                      ? `Vinculado com ${invite.invited_email}`
                      : `Vinculado com ${invite.requester_email}`
                  }
                  secondary={`Aceito em ${new Date(invite.updated_at || invite.created_at).toLocaleDateString('pt-BR')}`}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveLink(invite.id)}
                    title="Desvincular"
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* Dialog para Enviar Convite */}
      <Dialog open={openInviteDialog} onClose={() => setOpenInviteDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Convidar Membro da Família</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email do membro"
            type="email"
            fullWidth
            variant="outlined"
            value={inviteEmail}
            onChange={(e) => setInviteEmail(e.target.value)}
            placeholder="Digite o email da pessoa que deseja convidar"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenInviteDialog(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleSendInvite}
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Enviando...' : 'Enviar Convite'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FamilyManager;
