import React from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ptBR from 'date-fns/locale/pt-BR/index.js';

export interface TransactionFiltersProps {
  filters: {
    startDate: Date | null;
    endDate: Date | null;
    type: string;
    category: string;
    familyMember?: string;
  };
  onFilterChange: (filterName: keyof TransactionFiltersProps['filters'], value: any) => void;
  familyMembers?: Array<{id: number, name: string, email: string, is_self: boolean}>;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({
  filters,
  onFilterChange,
  familyMembers = []
}) => {
  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center',
      width: '100%' 
    }}>
      <Grid 
        container 
        spacing={2} 
        sx={{ 
          mb: 3, 
          width: '90%',  // Container mais largo para acomodar todos os itens
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <Grid item xs={12} md={2.5}>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
            <DatePicker
              label="Data Inicial"
              value={filters.startDate}
              onChange={(newValue) => {
                if (newValue) {
                  const date = new Date(newValue);
                  date.setHours(0, 0, 0, 0);
                  onFilterChange('startDate', date);
                } else {
                  onFilterChange('startDate', null);
                }
              }}
              slotProps={{
                textField: { 
                  fullWidth: true, 
                  size: 'small',
                }
              }}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} md={2.5}>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
            <DatePicker
              label="Data Final"
              value={filters.endDate}
              onChange={(newValue) => {
                if (newValue) {
                  const date = new Date(newValue);
                  date.setHours(23, 59, 59, 999);
                  onFilterChange('endDate', date);
                } else {
                  onFilterChange('endDate', null);
                }
              }}
              slotProps={{
                textField: { 
                  fullWidth: true, 
                  size: 'small',
                }
              }}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} md={2.5}>
          <FormControl fullWidth size="small">
            <InputLabel>Tipo</InputLabel>
            <Select
              value={filters.type}
              onChange={(e) => onFilterChange('type', e.target.value)}
              label="Tipo"
            >
              <MenuItem value="">Todos</MenuItem>
              <MenuItem value="income">Entrada</MenuItem>
              <MenuItem value="expense">Saída</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={2.5}>
          <TextField
            fullWidth
            size="small"
            label="Categoria"
            value={filters.category}
            onChange={(e) => onFilterChange('category', e.target.value)}
          />
        </Grid>

        {familyMembers.length > 1 && (
          <Grid item xs={12} md={2.5}>
            <FormControl fullWidth size="small">
              <InputLabel>Membro</InputLabel>
              <Select
                value={filters.familyMember || ''}
                onChange={(e) => onFilterChange('familyMember', e.target.value)}
                label="Membro"
              >
                <MenuItem value="">Todos</MenuItem>
                {familyMembers.map((member) => (
                  <MenuItem key={member.id} value={member.id.toString()}>
                    {member.is_self ? 'Você' : (member.name || member.email)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default TransactionFilters;