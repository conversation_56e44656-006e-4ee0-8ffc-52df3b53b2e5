import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Chip,
  Button,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Snackbar,
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
} from 'recharts';
import {
  ArrowUpward,
  ArrowDownward,
  Timeline as TimelineIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import api from '../../services/api';
import InvestmentManager from '../Investments/InvestmentManager';

interface Investment {
  id: number;
  name: string;
  type: string;
  initial_amount: number;
  current_amount: number;
  interest_rate?: number;
  start_date: string;
  end_date?: string;
}

interface InvestmentTransactionSummary {
  total_deposits: number;
  total_withdrawals: number;
  net_investment: number;
  total_profit_loss: number;
  profit_percentage: number;
}

interface InvestmentSummaryProps {
  investments?: any[]; // Transações de investimentos do componente pai
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#4CAF50', '#FF5722', '#9C27B0'];

const INVESTMENT_TYPES = {
  'savings': 'Poupança',
  'stocks': 'Ações',
  'bonds': 'Renda Fixa',
  'real_estate': 'Imóveis',
  'crypto': 'Criptomoedas',
  'funds': 'Fundos',
  'other': 'Outros'
};

const InvestmentSummary: React.FC<InvestmentSummaryProps> = ({ investments: investmentTransactions = [] }) => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [summary, setSummary] = useState<InvestmentTransactionSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [managerOpen, setManagerOpen] = useState(false);
  const [feedback, setFeedback] = useState<{ message: string, severity: 'success' | 'error' } | null>(null);

  useEffect(() => {
    // Se recebermos investimentos como prop, podemos utilizá-los para análises adicionais
    if (investmentTransactions.length > 0) {
      console.log('Transações de investimentos recebidas:', investmentTransactions);
    }

    loadInvestments();
    loadInvestmentSummary();
  }, [investmentTransactions]);

  const loadInvestments = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await api.get('/investments');
      setInvestments(response.data);
    } catch (err) {
      console.error('Erro ao carregar investimentos:', err);
      setError('Não foi possível carregar seus investimentos.');
    } finally {
      setLoading(false);
    }
  };

  const loadInvestmentSummary = async () => {
    try {
      const response = await api.get('/investments/summary');
      setSummary(response.data);
    } catch (err) {
      console.error('Erro ao carregar resumo de investimentos:', err);
    }
  };

  const handleOpenDetails = (investment: Investment) => {
    setSelectedInvestment(investment);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedInvestment(null);
  };

  const handleOpenManager = () => {
    setManagerOpen(true);
  };

  const handleCloseManager = () => {
    setManagerOpen(false);
    loadInvestments();
    loadInvestmentSummary();
  };

  const handleFeedback = (message: string, severity: 'success' | 'error') => {
    setFeedback({ message, severity });
  };

  const handleCloseFeedback = () => {
    setFeedback(null);
  };

  const calculateMonthlyReturn = (investment: Investment) => {
    if (!investment.start_date) return 0;

    const startDate = new Date(investment.start_date);
    const now = new Date();

    const monthsDiff = (now.getFullYear() - startDate.getFullYear()) * 12 +
                       now.getMonth() - startDate.getMonth();

    if (monthsDiff <= 0) return 0;

    const totalReturn = (investment.current_amount / investment.initial_amount) - 1;
    const monthlyReturn = Math.pow(1 + totalReturn, 1 / monthsDiff) - 1;

    return monthlyReturn * 100;
  };

  const investmentByTypeData = React.useMemo(() => {
    const typeMap = new Map<string, number>();

    investments.forEach(inv => {
      const type = inv.type || 'other';
      const currentAmount = typeMap.get(type) || 0;
      typeMap.set(type, currentAmount + inv.current_amount);
    });

    return Array.from(typeMap.entries()).map(([type, amount]) => ({
      name: INVESTMENT_TYPES[type as keyof typeof INVESTMENT_TYPES] || 'Outros',
      value: amount
    }));
  }, [investments]);

  const performanceData = React.useMemo(() => {
    return investments.map(inv => ({
      name: inv.name,
      profit: inv.current_amount - inv.initial_amount,
      return: calculateMonthlyReturn(inv)
    })).sort((a, b) => b.profit - a.profit);
  }, [investments]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading && !investments.length) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h5">Resumo de Investimentos</Typography>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<EditIcon />}
          onClick={handleOpenManager}
        >
          Gerenciar Investimentos
        </Button>
      </Box>

      {investments.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary" gutterBottom>
            Você ainda não tem investimentos cadastrados. Adicione seu primeiro investimento para começar a acompanhar seu patrimônio.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
            onClick={handleOpenManager}
          >
            Adicionar Investimento
          </Button>
        </Paper>
      ) : (
        <>
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Patrimônio Total
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {formatCurrency(investments.reduce((sum, inv) => sum + inv.current_amount, 0))}
                  </Typography>

                  {summary && (
                    <Box mt={2}>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">
                            Total Investido:
                          </Typography>
                          <Typography variant="body1" fontWeight="bold">
                            {formatCurrency(summary.total_deposits - summary.total_withdrawals)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">
                            Lucro Total:
                          </Typography>
                          <Typography
                            variant="body1"
                            fontWeight="bold"
                            color={summary.total_profit_loss >= 0 ? 'success.main' : 'error.main'}
                          >
                            {formatCurrency(summary.total_profit_loss)}
                            {' '}
                            <Typography
                              component="span"
                              variant="caption"
                              color={summary.profit_percentage >= 0 ? 'success.main' : 'error.main'}
                            >
                              ({summary.profit_percentage.toFixed(2)}%)
                            </Typography>
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Movimentações
                  </Typography>

                  {summary && (
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <ArrowDownward color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Total de Depósitos"
                          secondary={formatCurrency(summary.total_deposits)}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <ArrowUpward color="error" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Total de Resgates"
                          secondary={formatCurrency(summary.total_withdrawals)}
                        />
                      </ListItem>
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Distribuição por Tipo</Typography>
                  <Box height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={investmentByTypeData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          nameKey="name"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        >
                          {investmentByTypeData.map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value: number) => formatCurrency(value)}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Rendimento por Investimento</Typography>
                  <Box height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={performanceData.slice(0, 5)}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis type="category" dataKey="name" width={100} />
                        <Tooltip
                          formatter={(value: number, name: string) => [
                            name === 'profit' ?
                              formatCurrency(value) :
                              `${value.toFixed(2)}%`,
                            name === 'profit' ? 'Lucro' : 'Retorno Mensal'
                          ]}
                        />
                        <Legend />
                        <Bar
                          dataKey="profit"
                          name="Lucro"
                          fill="#00C49F"
                          barSize={20}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Box mt={4}>
            <Typography variant="h6" gutterBottom>Seus Investimentos</Typography>
            <Grid container spacing={2}>
              {investments.map(investment => (
                <Grid item xs={12} sm={6} md={4} key={investment.id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Typography variant="h6" component="div">
                          {investment.name}
                        </Typography>
                        <Chip
                          size="small"
                          label={INVESTMENT_TYPES[investment.type as keyof typeof INVESTMENT_TYPES] || "Outro"}
                          color="primary"
                          variant="outlined"
                        />
                      </Box>

                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Desde: {new Date(investment.start_date).toLocaleDateString()}
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="textSecondary">
                            Valor Inicial:
                          </Typography>
                          <Typography variant="body2">
                            {formatCurrency(investment.initial_amount)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="textSecondary">
                            Valor Atual:
                          </Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(investment.current_amount)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="textSecondary">
                            Rendimento:
                          </Typography>
                          <Typography
                            variant="body2"
                            color={investment.current_amount >= investment.initial_amount ? 'success.main' : 'error.main'}
                          >
                            {formatCurrency(investment.current_amount - investment.initial_amount)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="textSecondary">
                            Retorno:
                          </Typography>
                          <Typography
                            variant="body2"
                            color={investment.current_amount >= investment.initial_amount ? 'success.main' : 'error.main'}
                          >
                            {((investment.current_amount / investment.initial_amount - 1) * 100).toFixed(1)}%
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                    <Box p={1} display="flex" justifyContent="flex-end">
                      <Button
                        size="small"
                        onClick={() => handleOpenDetails(investment)}
                        startIcon={<TimelineIcon />}
                      >
                        Detalhes
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>

          {selectedInvestment && (
            <Dialog
              open={detailsOpen}
              onClose={handleCloseDetails}
              maxWidth="md"
              fullWidth
            >
              <DialogTitle>
                {selectedInvestment.name}
                <Typography variant="caption" display="block">
                  {INVESTMENT_TYPES[selectedInvestment.type as keyof typeof INVESTMENT_TYPES] || 'Outro'}
                </Typography>
              </DialogTitle>
              <DialogContent>
                <Box mb={3}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="caption" color="textSecondary">
                        Valor Inicial:
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {formatCurrency(selectedInvestment.initial_amount)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="caption" color="textSecondary">
                        Valor Atual:
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {formatCurrency(selectedInvestment.current_amount)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="caption" color="textSecondary">
                        Rendimento Total:
                      </Typography>
                      <Typography
                        variant="body1"
                        fontWeight="bold"
                        color={selectedInvestment.current_amount >= selectedInvestment.initial_amount ? 'success.main' : 'error.main'}
                      >
                        {formatCurrency(selectedInvestment.current_amount - selectedInvestment.initial_amount)}
                        {' '}
                        <Typography component="span" variant="caption">
                          ({((selectedInvestment.current_amount / selectedInvestment.initial_amount - 1) * 100).toFixed(1)}%)
                        </Typography>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="caption" color="textSecondary">
                        Taxa de Juros:
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {selectedInvestment.interest_rate ? `${selectedInvestment.interest_rate}%` : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  Rendimento Médio Mensal
                </Typography>
                <Typography
                  variant="h6"
                  color={calculateMonthlyReturn(selectedInvestment) >= 0 ? 'success.main' : 'error.main'}
                >
                  {calculateMonthlyReturn(selectedInvestment).toFixed(2)}% ao mês
                </Typography>

                <Divider sx={{ my: 3 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Projeção para 12 meses
                </Typography>
                <Box mt={2}>
                  <Typography variant="body1">
                    Valor projetado em 12 meses:
                    {' '}
                    <Typography component="span" fontWeight="bold">
                      {formatCurrency(
                        selectedInvestment.current_amount *
                        Math.pow(1 + calculateMonthlyReturn(selectedInvestment) / 100, 12)
                      )}
                    </Typography>
                  </Typography>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseDetails} color="primary">
                  Fechar
                </Button>
              </DialogActions>
            </Dialog>
          )}
        </>
      )}

      <Dialog
        open={managerOpen}
        onClose={handleCloseManager}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Gerenciar Investimentos
        </DialogTitle>
        <DialogContent>
          <InvestmentManager onFeedback={handleFeedback} />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseManager} color="primary">
            Fechar
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={!!feedback}
        autoHideDuration={6000}
        onClose={handleCloseFeedback}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseFeedback}
          severity={feedback?.severity || 'success'}
          sx={{ width: '100%' }}
        >
          {feedback?.message || ''}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default InvestmentSummary;