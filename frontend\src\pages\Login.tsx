import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TextField,
  Button,
  Paper,
  Typography,
  Box,
  Container,
  AppBar,
  Toolbar,
  Grid,
  Avatar,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import { LockOutlined as LockOutlinedIcon, Google as GoogleIcon } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const navigate = useNavigate();
  const { signIn } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const params = new URLSearchParams();
      params.append('username', email);
      params.append('password', password);

      const response = await api.post('/auth/token', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.data.access_token) {
        signIn(response.data.access_token);
        navigate('/dashboard');
      }
    } catch (err: any) {
      console.error('Erro ao fazer login:', err.response?.data || err.message);
      setError('Email ou senha inválidos');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setError('');
    setIsGoogleLoading(true);

    try {
      const response = await api.get('/auth/google/login');
      if (response.data.auth_url) {
        // Redirecionar para o Google OAuth
        window.location.href = response.data.auth_url;
      }
    } catch (err: any) {
      console.error('Erro ao iniciar login Google:', err.response?.data || err.message);
      setError('Erro ao conectar com Google. Tente novamente.');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppBar com largura 100% */}
      <AppBar position="static" sx={{ width: '100%' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Finance App
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Container centralizado com 80% de largura para consistência */}
      <Container maxWidth={false} sx={{ width: '80%', margin: '2rem auto', padding: 0 }}>
        <Grid container spacing={3}>
          {/* Card de boas-vindas */}
          <Grid item xs={12}>
            <Paper sx={{ p: 4, mb: 3, textAlign: 'center' }}>
              <Typography variant="h4" gutterBottom>
                Bem-vindo ao Finance App
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Faça login para gerenciar suas finanças de forma simples e eficiente
              </Typography>
            </Paper>
          </Grid>

          {/* Formulário de login */}
          <Grid item xs={12} md={6} sx={{ margin: '0 auto' }}>
            <Paper sx={{ p: 4 }}>
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%'
              }}>
                <Avatar sx={{ m: 1, bgcolor: 'primary.main', width: 56, height: 56 }}>
                  <LockOutlinedIcon sx={{ fontSize: 32 }} />
                </Avatar>

                <Typography component="h1" variant="h5" gutterBottom>
                  Acesso ao Sistema
                </Typography>

                <Box component="form"
                  onSubmit={handleSubmit}
                  sx={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    mt: 1
                  }}
                >
                  <TextField
                    margin="normal"
                    required
                    label="Email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    autoFocus
                    sx={{ mb: 2, width: '70%' }}
                  />

                  <TextField
                    margin="normal"
                    required
                    label="Senha"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={{ mb: 3, width: '70%' }}
                  />

                  {error && (
                    <Alert severity="error" sx={{ mb: 2, width: '70%' }}>
                      {error}
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={isLoading || isGoogleLoading}
                    sx={{ mb: 2, py: 1.5, width: '70%' }}
                  >
                    {isLoading ? <CircularProgress size={24} /> : "Entrar"}
                  </Button>

                  <Divider sx={{ width: '70%', my: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      ou
                    </Typography>
                  </Divider>

                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<GoogleIcon />}
                    onClick={handleGoogleLogin}
                    disabled={isLoading || isGoogleLoading}
                    sx={{
                      mb: 2,
                      py: 1.5,
                      width: '70%',
                      borderColor: '#db4437',
                      color: '#db4437',
                      '&:hover': {
                        borderColor: '#c23321',
                        backgroundColor: 'rgba(219, 68, 55, 0.04)'
                      }
                    }}
                  >
                    {isGoogleLoading ? <CircularProgress size={24} /> : "Entrar com Google"}
                  </Button>
                </Box>
              </Box>
            </Paper>
          </Grid>

          {/* Card informativo */}
          <Grid item xs={12}>
            <Paper sx={{ p: 4, mt: 3, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                Finance App - Sua plataforma completa para gestão financeira
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Login;