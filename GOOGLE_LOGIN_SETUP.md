# 🔐 Configuração do Login com Google - Finance App

Este documento explica como configurar e usar o login com Google no Finance App.

## 📋 **Pré-requisitos**

1. Conta no Google Cloud Console
2. Projeto criado no Google Cloud Console
3. Finance App rodando localmente

## 🚀 **Configuração no Google Cloud Console**

### 1. Acessar o Google Cloud Console
- Acesse: https://console.cloud.google.com/
- Faça login com sua conta Google

### 2. Criar ou Selecionar Projeto
- Crie um novo projeto ou selecione um existente
- Anote o nome do projeto para referência

### 3. Ativar APIs Necessárias
- Vá em **APIs & Services** > **Library**
- Procure e ative as seguintes APIs:
  - **Google+ API** (ou **Google Identity API**)
  - **Google OAuth2 API**

### 4. Configurar Tela de Consentimento OAuth
- Vá em **APIs & Services** > **OAuth consent screen**
- Escolha **External** (para uso geral) ou **Internal** (apenas para sua organização)
- Preencha as informações obrigatórias:
  - **App name**: Finance App
  - **User support email**: seu email
  - **Developer contact information**: seu email
- Salve e continue

### 5. Criar Credenciais OAuth 2.0
- Vá em **APIs & Services** > **Credentials**
- Clique em **+ CREATE CREDENTIALS** > **OAuth 2.0 Client IDs**
- Escolha **Web application**
- Configure:
  - **Name**: Finance App Web Client
  - **Authorized JavaScript origins**:
    - `http://localhost:3000`
    - `http://127.0.0.1:3000`
  - **Authorized redirect URIs**:
    - `http://localhost:8000/api/v1/auth/google/callback`
    - `http://127.0.0.1:8000/api/v1/auth/google/callback`

### 6. Obter Credenciais
- Após criar, você receberá:
  - **Client ID**: algo como `123456789-abcdef.apps.googleusercontent.com`
  - **Client Secret**: uma string aleatória
- **⚠️ IMPORTANTE**: Mantenha essas credenciais seguras!

## 🔧 **Configuração no Finance App**

### 1. Criar Arquivo de Credenciais
Crie um arquivo `oauth.txt` em um dos seguintes locais:

**Opção 1: Na raiz do projeto (recomendado para desenvolvimento)**
```
d:\IA\projects\finance-app\oauth.txt
```

**Opção 2: Na sua pasta home**
```
C:\Users\<USER>\oauth.txt
```

**Opção 3: Em Documents**
```
C:\Users\<USER>\Documents\oauth.txt
```

### 2. Formato do Arquivo oauth.txt
```
GOOGLE_CLIENT_ID=seu_client_id_aqui.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=sua_client_secret_aqui
```

**Exemplo:**
```
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-AbCdEfGhIjKlMnOpQrStUvWxYz
```

### 3. Verificar Configuração
1. Reinicie o servidor backend
2. Acesse: http://localhost:8000/api/v1/auth/google/status
3. Deve retornar:
```json
{
  "google_oauth_enabled": true,
  "client_id_configured": true,
  "client_secret_configured": true,
  "redirect_uri": "http://localhost:8000/api/v1/auth/google/callback"
}
```

## 🎯 **Como Usar o Login com Google**

### 1. Login Direto com Google
- Acesse: http://localhost:8000/api/v1/auth/google/login
- Será redirecionado para o Google
- Após autorizar, será criada uma conta automaticamente

### 2. Vincular Conta Existente
- Faça login normalmente no Finance App
- Use o endpoint `/api/v1/auth/google/link` para vincular sua conta Google

### 3. Gerenciamento de Usuários (Admin)
- Admins podem vincular contas Google a usuários existentes
- Use o painel de administração para gerenciar vinculações

## 🔒 **Segurança**

### ✅ **Boas Práticas**
- ✅ Arquivo `oauth.txt` está no `.gitignore`
- ✅ Credenciais ficam fora do código
- ✅ Verificação de state para prevenir CSRF
- ✅ Validação de tokens do Google

### ⚠️ **Cuidados**
- **NUNCA** commite o arquivo `oauth.txt`
- **NUNCA** compartilhe suas credenciais
- Mantenha as credenciais em local seguro
- Use HTTPS em produção

## 🐛 **Solução de Problemas**

### Erro: "Google OAuth não está configurado"
- Verifique se o arquivo `oauth.txt` existe
- Confirme se as credenciais estão corretas
- Reinicie o servidor backend

### Erro: "redirect_uri_mismatch"
- Verifique se as URLs de redirecionamento estão corretas no Google Cloud Console
- Confirme se está usando `http://localhost:8000` (não `127.0.0.1`)

### Erro: "invalid_client"
- Verifique se o Client ID e Client Secret estão corretos
- Confirme se as APIs estão ativadas no Google Cloud Console

### Status sempre mostra "não configurado"
- Verifique se o arquivo `oauth.txt` está em um dos locais suportados
- Confirme o formato do arquivo (chave=valor)
- Verifique os logs do servidor para erros

## 📞 **Suporte**

Se encontrar problemas:
1. Verifique os logs do servidor backend
2. Teste o endpoint `/api/v1/auth/google/status`
3. Confirme as configurações no Google Cloud Console
4. Verifique se o arquivo `oauth.txt` está correto

## 🔄 **Fluxo Completo**

1. **Usuário clica em "Login com Google"**
2. **Sistema redireciona para Google**
3. **Usuário autoriza no Google**
4. **Google redireciona de volta com código**
5. **Sistema troca código por token**
6. **Sistema obtém dados do usuário**
7. **Sistema cria/atualiza usuário local**
8. **Sistema retorna token JWT para o frontend**

---

**✅ Configuração concluída!** Agora você pode usar o login com Google no Finance App.
