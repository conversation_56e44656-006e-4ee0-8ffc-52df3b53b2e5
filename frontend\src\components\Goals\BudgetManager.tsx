import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  TextField,
  IconButton,
  LinearProgress,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Tooltip,
  Alert,
  FormHelperText,
  Divider,
  SelectChangeEvent,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import api from '../../services/api';

interface Budget {
  id: number;
  category: string;
  monthly_limit: number;
  start_date: string;
  end_date: string | null;
  user_id: number;
  current_spending: number; // Calculado no frontend
  remaining: number; // Calculado no frontend
  percentage: number; // Calculado no frontend
}

interface BudgetFormData {
  category: string;
  monthly_limit: number;
  end_date: Date | null;
}

interface CategorySummary {
  category: string;
  amount: number;
  percentage: number;
}

interface BudgetManagerProps {
  onFeedback?: (message: string, severity: 'success' | 'error') => void;
}

const DEFAULT_CATEGORIES = [
  'Alimentação',
  'Transporte',
  'Moradia',
  'Saúde',
  'Educação',
  'Lazer',
  'Investimentos',
  'Cartão de Crédito',
  'Outros',
];

const BudgetManager: React.FC<BudgetManagerProps> = ({ onFeedback }) => {
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [categorySummaries, setCategorySummaries] = useState<CategorySummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Estado para o formulário de orçamento
  const [formOpen, setFormOpen] = useState(false);
  const [currentBudget, setCurrentBudget] = useState<Budget | null>(null);
  const [formData, setFormData] = useState<BudgetFormData>({
    category: '',
    monthly_limit: 0,
    end_date: null,
  });

  // Estado para o diálogo de confirmação de exclusão
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [budgetToDelete, setBudgetToDelete] = useState<Budget | null>(null);

  // Carregar orçamentos e resumos de categorias ao montar o componente
  useEffect(() => {
    loadBudgets();
    loadCategorySummaries();
  }, []);

  // Função para carregar os orçamentos do usuário
  const loadBudgets = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/budgets/');
      const budgetsData = response.data;

      // Buscar os gastos atuais para cada categoria
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const spendingResponse = await api.get('/transactions/summary', {
        params: {
          start_date: firstDay.toISOString(),
          end_date: lastDay.toISOString(),
          type: 'expense'
        }
      });

      const categorySpending = spendingResponse.data.expenses_by_category.reduce(
        (acc: Record<string, number>, item: CategorySummary) => {
          acc[item.category] = item.amount;
          return acc;
        },
        {}
      );

      // Calcular valores para exibição
      const enhancedBudgets = budgetsData.map((budget: Budget) => {
        const spending = categorySpending[budget.category] || 0;
        const remaining = Math.max(0, budget.monthly_limit - spending);
        const percentage = budget.monthly_limit > 0
          ? Math.min(100, (spending / budget.monthly_limit) * 100)
          : 0;

        return {
          ...budget,
          current_spending: spending,
          remaining,
          percentage,
        };
      });

      setBudgets(enhancedBudgets);
    } catch (err) {
      console.error('Erro ao carregar orçamentos:', err);
      setError('Erro ao carregar orçamentos. Tente novamente.');
      if (onFeedback) {
        onFeedback('Erro ao carregar orçamentos', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  // Função para carregar resumos de categorias
  const loadCategorySummaries = async () => {
    try {
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const response = await api.get('/transactions/summary', {
        params: {
          start_date: firstDay.toISOString(),
          end_date: lastDay.toISOString(),
        }
      });

      setCategorySummaries(response.data.expenses_by_category);
    } catch (err) {
      console.error('Erro ao carregar resumo de categorias:', err);
    }
  };

  // Handler para abrir o formulário de orçamento
  const handleOpenForm = (budget?: Budget) => {
    if (budget) {
      setCurrentBudget(budget);
      setFormData({
        category: budget.category,
        monthly_limit: budget.monthly_limit,
        end_date: budget.end_date ? new Date(budget.end_date) : null,
      });
    } else {
      setCurrentBudget(null);
      setFormData({
        category: '',
        monthly_limit: 0,
        end_date: null,
      });
    }
    setFormOpen(true);
  };

  // Handler para fechar o formulário
  const handleCloseForm = () => {
    setFormOpen(false);
    setCurrentBudget(null);
  };

  // Handler para mudanças nos campos do formulário
  const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>) => {
    const { name, value } = event.target;
    setFormData(prev => ({ ...prev, [name as string]: value }));
  };

  // Handler para criar/atualizar orçamento
  const handleSubmitBudget = async () => {
    try {
      const data = {
        ...formData,
        end_date: formData.end_date ? formData.end_date.toISOString() : null,
      };

      if (currentBudget) {
        // Atualizar orçamento existente
        await api.put(`/budgets/${currentBudget.id}/`, data);
        if (onFeedback) {
          onFeedback('Orçamento atualizado com sucesso!', 'success');
        }
      } else {
        // Criar novo orçamento
        await api.post('/budgets/', data);
        if (onFeedback) {
          onFeedback('Orçamento criado com sucesso!', 'success');
        }
      }

      // Recarregar orçamentos
      await loadBudgets();
      handleCloseForm();
    } catch (err) {
      console.error('Erro ao salvar orçamento:', err);
      if (onFeedback) {
        onFeedback('Erro ao salvar orçamento', 'error');
      }
    }
  };

  // Handler para abrir o diálogo de confirmação de exclusão
  const handleDeleteConfirmation = (budget: Budget) => {
    setBudgetToDelete(budget);
    setDeleteDialogOpen(true);
  };

  // Handler para excluir orçamento
  const handleDeleteBudget = async () => {
    if (!budgetToDelete) return;

    try {
      await api.delete(`/budgets/${budgetToDelete.id}/`);
      await loadBudgets();
      setDeleteDialogOpen(false);
      setBudgetToDelete(null);

      if (onFeedback) {
        onFeedback('Orçamento excluído com sucesso!', 'success');
      }
    } catch (err) {
      console.error('Erro ao excluir orçamento:', err);
      if (onFeedback) {
        onFeedback('Erro ao excluir orçamento', 'error');
      }
    }
  };

  // Função para formatar valores monetários
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Função para determinar a cor da barra de progresso
  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'error';
    if (percentage >= 75) return 'warning';
    return 'primary';
  };

  // Categorias sem orçamento definido
  const categoriesWithoutBudget = DEFAULT_CATEGORIES.filter(
    category => !budgets.some(budget => budget.category === category)
  );

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Orçamentos Mensais</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenForm()}
        >
          Novo Orçamento
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Indicador de carregamento */}
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <LinearProgress sx={{ width: '100%' }} />
        </Box>
      )}

      {/* Orçamentos existentes */}
      {!loading && budgets.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            Você ainda não definiu nenhum orçamento. Defina limites mensais de gastos por categoria para ajudar a controlar suas finanças.
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
            sx={{ mt: 2 }}
          >
            Criar Primeiro Orçamento
          </Button>
        </Paper>
      ) : (
        <>
          <Typography variant="h6" gutterBottom>
            Orçamento do Mês Atual
          </Typography>
          <Grid container spacing={3} mb={4}>
            {budgets.map(budget => (
              <Grid item xs={12} md={6} key={budget.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="h6">{budget.category}</Typography>
                      <Box>
                        <Tooltip title="Editar">
                          <IconButton size="small" onClick={() => handleOpenForm(budget)}>
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Excluir">
                          <IconButton size="small" color="error" onClick={() => handleDeleteConfirmation(budget)}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>

                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">
                        Limite: {formatCurrency(budget.monthly_limit)}
                      </Typography>
                      <Typography
                        variant="body2"
                        color={budget.percentage >= 100 ? 'error' : 'inherit'}
                        fontWeight="bold"
                      >
                        {budget.percentage.toFixed(1)}% utilizado
                      </Typography>
                    </Box>

                    <LinearProgress
                      variant="determinate"
                      value={Math.min(100, budget.percentage)}
                      color={getProgressColor(budget.percentage)}
                      sx={{ my: 1, height: 8, borderRadius: 1 }}
                    />

                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">
                        Gasto: {formatCurrency(budget.current_spending)}
                      </Typography>
                      <Typography
                        variant="body2"
                        color={budget.remaining > 0 ? 'success.main' : 'error.main'}
                      >
                        {budget.remaining > 0
                          ? `Restante: ${formatCurrency(budget.remaining)}`
                          : `Excedido: ${formatCurrency(Math.abs(budget.monthly_limit - budget.current_spending))}`}
                      </Typography>
                    </Box>

                    {budget.percentage >= 90 && (
                      <Box display="flex" alignItems="center" mt={1} sx={{ color: 'warning.main' }}>
                        <WarningIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="caption">
                          {budget.percentage >= 100
                            ? 'Orçamento excedido!'
                            : 'Quase atingindo o limite!'}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Sugestões de categorias para adicionar orçamentos */}
          {categoriesWithoutBudget.length > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Sugestões de Orçamentos
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                Ainda não há orçamentos definidos para as seguintes categorias:
              </Typography>
              <Grid container spacing={2}>
                {categoriesWithoutBudget.map(category => {
                  const categorySummary = categorySummaries.find(s => s.category === category);
                  const currentSpending = categorySummary?.amount || 0;

                  return (
                    <Grid item xs={12} md={4} key={category}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle1">{category}</Typography>
                        {currentSpending > 0 && (
                          <Typography variant="body2" color="textSecondary">
                            Gasto atual: {formatCurrency(currentSpending)}
                          </Typography>
                        )}
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => handleOpenForm({
                            id: 0,
                            category,
                            monthly_limit: currentSpending > 0 ? Math.ceil(currentSpending * 1.1) : 1000,
                            start_date: new Date().toISOString(),
                            end_date: null,
                            user_id: 0,
                            current_spending: currentSpending,
                            remaining: 0,
                            percentage: 0
                          })}
                          sx={{ mt: 1 }}
                        >
                          Definir Orçamento
                        </Button>
                      </Paper>
                    </Grid>
                  );
                })}
              </Grid>
            </>
          )}
        </>
      )}

      {/* Formulário para criar/editar orçamento */}
      <Dialog open={formOpen} onClose={handleCloseForm} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentBudget ? 'Editar Orçamento' : 'Novo Orçamento'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="category-label">Categoria</InputLabel>
                <Select
                  labelId="category-label"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleFormChange}
                  label="Categoria"
                >
                  {DEFAULT_CATEGORIES.map(category => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                name="monthly_limit"
                label="Limite Mensal"
                type="number"
                value={formData.monthly_limit}
                onChange={handleFormChange}
                fullWidth
                InputProps={{
                  startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                }}
              />
              <FormHelperText>
                Este valor representa o máximo que você deseja gastar nesta categoria por mês.
              </FormHelperText>
            </Grid>

            {/* Aqui poderia adicionar um DatePicker para end_date se necessário */}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm}>Cancelar</Button>
          <Button
            onClick={handleSubmitBudget}
            variant="contained"
            color="primary"
            disabled={!formData.category || formData.monthly_limit <= 0}
          >
            Salvar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o orçamento para {budgetToDelete?.category}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={handleDeleteBudget} color="error" variant="contained">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BudgetManager;